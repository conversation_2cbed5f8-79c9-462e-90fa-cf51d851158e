
// --------------------------
// 工具生成
// --------------------------

package msg
import "kairo_paradise_server/services/pb"
type PCK int32
const (
	PCK_S2GEmpty PCK = 100
	PCK_G2CError PCK = 999
	PCK_S2GResponse PCK = 998
	PCK_C2GHeartbeat PCK = 1000
	PCK_G2CHeartbeat PCK = 1001
	PCK_G2SInit PCK = 1002
	PCK_G2SDisconnect PCK = 1003
	PCK_S2CCommResponse PCK = 10000
	PCK_C2SEnterGame PCK = 10001
	PCK_S2CEnterGame PCK = 10002
	PCK_S2CPlayerInfo PCK = 10003
	PCK_S2CLoginFinish PCK = 10004
	PCK_C2SPlayerModifyInfo PCK = 10005
	PCK_S2CPlayerModifyLevel PCK = 10006
	PCK_S2CPlayerProsperityUpdate PCK = 10007
	PCK_S2CBackpackInit PCK = 10100
	PCK_S2CBackpackUpdate<PERSON><PERSON>n PCK = 10101
	PCK_S2CBackpackAddItem PCK = 10102
	PCK_S2CBackpackSubItem PCK = 10103
	PCK_C2SBackpackUseItem PCK = 10104
	PCK_C2SMailList PCK = 10160
	PCK_S2CMailList PCK = 10161
	PCK_C2SMailRead PCK = 10162
	PCK_C2SMailClaim PCK = 10164
	PCK_S2CMailDelete PCK = 10165
	PCK_C2SMailDelete PCK = 10166
	PCK_S2CMailNotify PCK = 10167
	PCK_C2SReadAllMail PCK = 10168
	PCK_C2SClaimMailAll PCK = 10169
	PCK_C2SDelAllMail PCK = 10170
	PCK_S2CAnnouncementList PCK = 10200
	PCK_C2SAnnouncementRead PCK = 10201
	PCK_S2CAnnouncementRead PCK = 10202
	PCK_S2CAnnouncementNotify PCK = 10203
	PCK_S2CBuildDataInit PCK = 10260
	PCK_C2SBuildDataSave PCK = 10261
	PCK_C2SClearBuild PCK = 10262
	PCK_S2CGameCenterInit PCK = 10300
	PCK_S2CGameCenterUpdate PCK = 10301
	PCK_C2SGameCenterBuyMiniGame PCK = 10302
	PCK_S2CGameCenterBuyMiniGame PCK = 10303
	PCK_C2SGameCenterStartMiniGame PCK = 10304
	PCK_C2SGameCenterEndMiniGame PCK = 10305
	PCK_C2SGM PCK = 10350
	PCK_C2SGMSendMail PCK = 10351
	PCK_G2SUpdateRank PCK = 31000
	PCK_C2SGetRankList PCK = 31001
	PCK_S2CGetRankList PCK = 31002
	PCK_C2SGetPlayerRank PCK = 31003
	PCK_S2CGetPlayerRank PCK = 31004
)


func onInit() {
	S2CProcessor.Register(uint16(PCK_S2GEmpty), &pb.S2GEmpty{})
	S2CProcessor.Register(uint16(PCK_G2CError), &pb.G2CError{})
	S2CProcessor.Register(uint16(PCK_S2GResponse), &pb.S2GResponse{})
	C2SProcessor.Register(uint16(PCK_C2GHeartbeat), &pb.C2GHeartbeat{})
	S2CProcessor.Register(uint16(PCK_G2CHeartbeat), &pb.G2CHeartbeat{})
	C2SProcessor.Register(uint16(PCK_G2SInit), &pb.G2SInit{})
	C2SProcessor.Register(uint16(PCK_G2SDisconnect), &pb.G2SDisconnect{})
	S2CProcessor.Register(uint16(PCK_S2CCommResponse), &pb.S2CCommResponse{})
	C2SProcessor.Register(uint16(PCK_C2SEnterGame), &pb.C2SEnterGame{})
	S2CProcessor.Register(uint16(PCK_S2CEnterGame), &pb.S2CEnterGame{})
	S2CProcessor.Register(uint16(PCK_S2CPlayerInfo), &pb.S2CPlayerInfo{})
	S2CProcessor.Register(uint16(PCK_S2CLoginFinish), &pb.S2CLoginFinish{})
	C2SProcessor.Register(uint16(PCK_C2SPlayerModifyInfo), &pb.C2SPlayerModifyInfo{})
	S2CProcessor.Register(uint16(PCK_S2CPlayerModifyLevel), &pb.S2CPlayerModifyLevel{})
	S2CProcessor.Register(uint16(PCK_S2CPlayerProsperityUpdate), &pb.S2CPlayerProsperityUpdate{})
	S2CProcessor.Register(uint16(PCK_S2CBackpackInit), &pb.S2CBackpackInit{})
	S2CProcessor.Register(uint16(PCK_S2CBackpackUpdateCoin), &pb.S2CBackpackUpdateCoin{})
	S2CProcessor.Register(uint16(PCK_S2CBackpackAddItem), &pb.S2CBackpackAddItem{})
	S2CProcessor.Register(uint16(PCK_S2CBackpackSubItem), &pb.S2CBackpackSubItem{})
	C2SProcessor.Register(uint16(PCK_C2SBackpackUseItem), &pb.C2SBackpackUseItem{})
	C2SProcessor.Register(uint16(PCK_C2SMailList), &pb.C2SMailList{})
	S2CProcessor.Register(uint16(PCK_S2CMailList), &pb.S2CMailList{})
	C2SProcessor.Register(uint16(PCK_C2SMailRead), &pb.C2SMailRead{})
	C2SProcessor.Register(uint16(PCK_C2SMailClaim), &pb.C2SMailClaim{})
	S2CProcessor.Register(uint16(PCK_S2CMailDelete), &pb.S2CMailDelete{})
	C2SProcessor.Register(uint16(PCK_C2SMailDelete), &pb.C2SMailDelete{})
	S2CProcessor.Register(uint16(PCK_S2CMailNotify), &pb.S2CMailNotify{})
	C2SProcessor.Register(uint16(PCK_C2SReadAllMail), &pb.C2SReadAllMail{})
	C2SProcessor.Register(uint16(PCK_C2SClaimMailAll), &pb.C2SClaimMailAll{})
	C2SProcessor.Register(uint16(PCK_C2SDelAllMail), &pb.C2SDelAllMail{})
	S2CProcessor.Register(uint16(PCK_S2CAnnouncementList), &pb.S2CAnnouncementList{})
	C2SProcessor.Register(uint16(PCK_C2SAnnouncementRead), &pb.C2SAnnouncementRead{})
	S2CProcessor.Register(uint16(PCK_S2CAnnouncementRead), &pb.S2CAnnouncementRead{})
	S2CProcessor.Register(uint16(PCK_S2CAnnouncementNotify), &pb.S2CAnnouncementNotify{})
	S2CProcessor.Register(uint16(PCK_S2CBuildDataInit), &pb.S2CBuildDataInit{})
	C2SProcessor.Register(uint16(PCK_C2SBuildDataSave), &pb.C2SBuildDataSave{})
	C2SProcessor.Register(uint16(PCK_C2SClearBuild), &pb.C2SClearBuild{})
	S2CProcessor.Register(uint16(PCK_S2CGameCenterInit), &pb.S2CGameCenterInit{})
	S2CProcessor.Register(uint16(PCK_S2CGameCenterUpdate), &pb.S2CGameCenterUpdate{})
	C2SProcessor.Register(uint16(PCK_C2SGameCenterBuyMiniGame), &pb.C2SGameCenterBuyMiniGame{})
	S2CProcessor.Register(uint16(PCK_S2CGameCenterBuyMiniGame), &pb.S2CGameCenterBuyMiniGame{})
	C2SProcessor.Register(uint16(PCK_C2SGameCenterStartMiniGame), &pb.C2SGameCenterStartMiniGame{})
	C2SProcessor.Register(uint16(PCK_C2SGameCenterEndMiniGame), &pb.C2SGameCenterEndMiniGame{})
	C2SProcessor.Register(uint16(PCK_C2SGM), &pb.C2SGM{})
	C2SProcessor.Register(uint16(PCK_C2SGMSendMail), &pb.C2SGMSendMail{})
	C2SProcessor.Register(uint16(PCK_G2SUpdateRank), &pb.G2SUpdateRank{})
	C2SProcessor.Register(uint16(PCK_C2SGetRankList), &pb.C2SGetRankList{})
	S2CProcessor.Register(uint16(PCK_S2CGetRankList), &pb.S2CGetRankList{})
	C2SProcessor.Register(uint16(PCK_C2SGetPlayerRank), &pb.C2SGetPlayerRank{})
	S2CProcessor.Register(uint16(PCK_S2CGetPlayerRank), &pb.S2CGetPlayerRank{})
}