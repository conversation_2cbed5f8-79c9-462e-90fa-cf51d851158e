// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: achievement.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 使用现有的响应码枚举
type ResponseCode int32

const (
	ResponseCode_normal     ResponseCode = 0
	ResponseCode_fail       ResponseCode = 1
	ResponseCode_params_err ResponseCode = 100
)

// Enum value maps for ResponseCode.
var (
	ResponseCode_name = map[int32]string{
		0:   "normal",
		1:   "fail",
		100: "params_err",
	}
	ResponseCode_value = map[string]int32{
		"normal":     0,
		"fail":       1,
		"params_err": 100,
	}
)

func (x ResponseCode) Enum() *ResponseCode {
	p := new(ResponseCode)
	*p = x
	return p
}

func (x ResponseCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResponseCode) Descriptor() protoreflect.EnumDescriptor {
	return file_achievement_proto_enumTypes[0].Descriptor()
}

func (ResponseCode) Type() protoreflect.EnumType {
	return &file_achievement_proto_enumTypes[0]
}

func (x ResponseCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ResponseCode) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ResponseCode(num)
	return nil
}

// Deprecated: Use ResponseCode.Descriptor instead.
func (ResponseCode) EnumDescriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{0}
}

// 成就信息
type AchievementInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`                                                 // 成就ID
	Name           *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`                                              // 成就名称
	Description    *string                `protobuf:"bytes,3,opt,name=description" json:"description,omitempty"`                                // 成就描述
	GameId         *int32                 `protobuf:"varint,4,opt,name=game_id,json=gameId" json:"game_id,omitempty"`                           // 所属游戏ID
	GameName       *string                `protobuf:"bytes,5,opt,name=game_name,json=gameName" json:"game_name,omitempty"`                      // 游戏名称
	Icon           *string                `protobuf:"bytes,6,opt,name=icon" json:"icon,omitempty"`                                              // 成就图标
	Rarity         *int32                 `protobuf:"varint,7,opt,name=rarity" json:"rarity,omitempty"`                                         // 稀有度
	Status         *int32                 `protobuf:"varint,8,opt,name=status" json:"status,omitempty"`                                         // 状态 0=未完成 1=可领取 2=已领取
	Progress       *int64                 `protobuf:"varint,9,opt,name=progress" json:"progress,omitempty"`                                     // 当前进度
	MaxProgress    *int64                 `protobuf:"varint,10,opt,name=max_progress,json=maxProgress" json:"max_progress,omitempty"`           // 最大进度
	CompletedAt    *int64                 `protobuf:"varint,11,opt,name=completed_at,json=completedAt" json:"completed_at,omitempty"`           // 完成时间
	ClaimedAt      *int64                 `protobuf:"varint,12,opt,name=claimed_at,json=claimedAt" json:"claimed_at,omitempty"`                 // 领取时间
	CompletionRate *float32               `protobuf:"fixed32,13,opt,name=completion_rate,json=completionRate" json:"completion_rate,omitempty"` // 达成百分比 (完成人数/玩该游戏人数)
	Rewards        []*ItemData            `protobuf:"bytes,14,rep,name=rewards" json:"rewards,omitempty"`                                       // 奖励列表
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AchievementInfo) Reset() {
	*x = AchievementInfo{}
	mi := &file_achievement_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AchievementInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AchievementInfo) ProtoMessage() {}

func (x *AchievementInfo) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AchievementInfo.ProtoReflect.Descriptor instead.
func (*AchievementInfo) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{0}
}

func (x *AchievementInfo) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *AchievementInfo) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *AchievementInfo) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *AchievementInfo) GetGameId() int32 {
	if x != nil && x.GameId != nil {
		return *x.GameId
	}
	return 0
}

func (x *AchievementInfo) GetGameName() string {
	if x != nil && x.GameName != nil {
		return *x.GameName
	}
	return ""
}

func (x *AchievementInfo) GetIcon() string {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return ""
}

func (x *AchievementInfo) GetRarity() int32 {
	if x != nil && x.Rarity != nil {
		return *x.Rarity
	}
	return 0
}

func (x *AchievementInfo) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *AchievementInfo) GetProgress() int64 {
	if x != nil && x.Progress != nil {
		return *x.Progress
	}
	return 0
}

func (x *AchievementInfo) GetMaxProgress() int64 {
	if x != nil && x.MaxProgress != nil {
		return *x.MaxProgress
	}
	return 0
}

func (x *AchievementInfo) GetCompletedAt() int64 {
	if x != nil && x.CompletedAt != nil {
		return *x.CompletedAt
	}
	return 0
}

func (x *AchievementInfo) GetClaimedAt() int64 {
	if x != nil && x.ClaimedAt != nil {
		return *x.ClaimedAt
	}
	return 0
}

func (x *AchievementInfo) GetCompletionRate() float32 {
	if x != nil && x.CompletionRate != nil {
		return *x.CompletionRate
	}
	return 0
}

func (x *AchievementInfo) GetRewards() []*ItemData {
	if x != nil {
		return x.Rewards
	}
	return nil
}

// 稀缺掉落信息
type RareDropInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *uint64                `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`                                   // 稀缺掉落ID
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`                                // 掉落名称
	Description   *string                `protobuf:"bytes,3,opt,name=description" json:"description,omitempty"`                  // 掉落描述
	Icon          *string                `protobuf:"bytes,4,opt,name=icon" json:"icon,omitempty"`                                // 掉落图标
	Rarity        *int32                 `protobuf:"varint,5,opt,name=rarity" json:"rarity,omitempty"`                           // 稀有度
	ObtainedAt    *int64                 `protobuf:"varint,6,opt,name=obtained_at,json=obtainedAt" json:"obtained_at,omitempty"` // 获得时间
	Source        *string                `protobuf:"bytes,7,opt,name=source" json:"source,omitempty"`                            // 获得来源
	Rewards       []*ItemData            `protobuf:"bytes,8,rep,name=rewards" json:"rewards,omitempty"`                          // 奖励列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RareDropInfo) Reset() {
	*x = RareDropInfo{}
	mi := &file_achievement_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RareDropInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RareDropInfo) ProtoMessage() {}

func (x *RareDropInfo) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RareDropInfo.ProtoReflect.Descriptor instead.
func (*RareDropInfo) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{1}
}

func (x *RareDropInfo) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *RareDropInfo) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *RareDropInfo) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *RareDropInfo) GetIcon() string {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return ""
}

func (x *RareDropInfo) GetRarity() int32 {
	if x != nil && x.Rarity != nil {
		return *x.Rarity
	}
	return 0
}

func (x *RareDropInfo) GetObtainedAt() int64 {
	if x != nil && x.ObtainedAt != nil {
		return *x.ObtainedAt
	}
	return 0
}

func (x *RareDropInfo) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *RareDropInfo) GetRewards() []*ItemData {
	if x != nil {
		return x.Rewards
	}
	return nil
}

// 获取成就列表请求 10400
type C2SAchievementList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TabType       *int32                 `protobuf:"varint,1,opt,name=tab_type,json=tabType" json:"tab_type,omitempty"`                  // 标签类型 0=全部 1=未完成 2=已领取 3=可领取 4=稀缺掉落
	GameId        *int32                 `protobuf:"varint,2,opt,name=game_id,json=gameId" json:"game_id,omitempty"`                     // 游戏ID筛选，0表示所有游戏
	Page          *int32                 `protobuf:"varint,3,opt,name=page" json:"page,omitempty"`                                       // 页码，从1开始
	PageSize      *int32                 `protobuf:"varint,4,opt,name=page_size,json=pageSize" json:"page_size,omitempty"`               // 每页数量，默认20
	SearchKeyword *string                `protobuf:"bytes,5,opt,name=search_keyword,json=searchKeyword" json:"search_keyword,omitempty"` // 搜索关键词
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SAchievementList) Reset() {
	*x = C2SAchievementList{}
	mi := &file_achievement_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SAchievementList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SAchievementList) ProtoMessage() {}

func (x *C2SAchievementList) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SAchievementList.ProtoReflect.Descriptor instead.
func (*C2SAchievementList) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{2}
}

func (x *C2SAchievementList) GetTabType() int32 {
	if x != nil && x.TabType != nil {
		return *x.TabType
	}
	return 0
}

func (x *C2SAchievementList) GetGameId() int32 {
	if x != nil && x.GameId != nil {
		return *x.GameId
	}
	return 0
}

func (x *C2SAchievementList) GetPage() int32 {
	if x != nil && x.Page != nil {
		return *x.Page
	}
	return 0
}

func (x *C2SAchievementList) GetPageSize() int32 {
	if x != nil && x.PageSize != nil {
		return *x.PageSize
	}
	return 0
}

func (x *C2SAchievementList) GetSearchKeyword() string {
	if x != nil && x.SearchKeyword != nil {
		return *x.SearchKeyword
	}
	return ""
}

// 获取成就列表响应 10401
type S2CAchievementList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,opt,name=code,enum=pb.ResponseCode" json:"code,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	Achievements  []*AchievementInfo     `protobuf:"bytes,3,rep,name=achievements" json:"achievements,omitempty"`                   // 成就列表
	RareDrops     []*RareDropInfo        `protobuf:"bytes,4,rep,name=rare_drops,json=rareDrops" json:"rare_drops,omitempty"`        // 稀缺掉落列表
	TotalCount    *int32                 `protobuf:"varint,5,opt,name=total_count,json=totalCount" json:"total_count,omitempty"`    // 总数量
	CurrentPage   *int32                 `protobuf:"varint,6,opt,name=current_page,json=currentPage" json:"current_page,omitempty"` // 当前页码
	TotalPages    *int32                 `protobuf:"varint,7,opt,name=total_pages,json=totalPages" json:"total_pages,omitempty"`    // 总页数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CAchievementList) Reset() {
	*x = S2CAchievementList{}
	mi := &file_achievement_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CAchievementList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CAchievementList) ProtoMessage() {}

func (x *S2CAchievementList) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CAchievementList.ProtoReflect.Descriptor instead.
func (*S2CAchievementList) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{3}
}

func (x *S2CAchievementList) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2CAchievementList) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *S2CAchievementList) GetAchievements() []*AchievementInfo {
	if x != nil {
		return x.Achievements
	}
	return nil
}

func (x *S2CAchievementList) GetRareDrops() []*RareDropInfo {
	if x != nil {
		return x.RareDrops
	}
	return nil
}

func (x *S2CAchievementList) GetTotalCount() int32 {
	if x != nil && x.TotalCount != nil {
		return *x.TotalCount
	}
	return 0
}

func (x *S2CAchievementList) GetCurrentPage() int32 {
	if x != nil && x.CurrentPage != nil {
		return *x.CurrentPage
	}
	return 0
}

func (x *S2CAchievementList) GetTotalPages() int32 {
	if x != nil && x.TotalPages != nil {
		return *x.TotalPages
	}
	return 0
}

// 领取成就奖励请求 10402
type C2SAchievementClaim struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AchievementId *uint64                `protobuf:"varint,1,opt,name=achievement_id,json=achievementId" json:"achievement_id,omitempty"` // 成就ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SAchievementClaim) Reset() {
	*x = C2SAchievementClaim{}
	mi := &file_achievement_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SAchievementClaim) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SAchievementClaim) ProtoMessage() {}

func (x *C2SAchievementClaim) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SAchievementClaim.ProtoReflect.Descriptor instead.
func (*C2SAchievementClaim) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{4}
}

func (x *C2SAchievementClaim) GetAchievementId() uint64 {
	if x != nil && x.AchievementId != nil {
		return *x.AchievementId
	}
	return 0
}

// 领取成就奖励响应 10403
type S2CAchievementClaim struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,opt,name=code,enum=pb.ResponseCode" json:"code,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	AchievementId *uint64                `protobuf:"varint,3,opt,name=achievement_id,json=achievementId" json:"achievement_id,omitempty"` // 成就ID
	Rewards       []*ItemData            `protobuf:"bytes,4,rep,name=rewards" json:"rewards,omitempty"`                                   // 获得的奖励
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CAchievementClaim) Reset() {
	*x = S2CAchievementClaim{}
	mi := &file_achievement_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CAchievementClaim) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CAchievementClaim) ProtoMessage() {}

func (x *S2CAchievementClaim) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CAchievementClaim.ProtoReflect.Descriptor instead.
func (*S2CAchievementClaim) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{5}
}

func (x *S2CAchievementClaim) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2CAchievementClaim) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *S2CAchievementClaim) GetAchievementId() uint64 {
	if x != nil && x.AchievementId != nil {
		return *x.AchievementId
	}
	return 0
}

func (x *S2CAchievementClaim) GetRewards() []*ItemData {
	if x != nil {
		return x.Rewards
	}
	return nil
}

// 成就进度更新通知
type S2CAchievementProgress struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AchievementId *uint64                `protobuf:"varint,1,opt,name=achievement_id,json=achievementId" json:"achievement_id,omitempty"` // 成就ID
	Progress      *int64                 `protobuf:"varint,2,opt,name=progress" json:"progress,omitempty"`                                // 当前进度
	MaxProgress   *int64                 `protobuf:"varint,3,opt,name=max_progress,json=maxProgress" json:"max_progress,omitempty"`       // 最大进度
	Status        *int32                 `protobuf:"varint,4,opt,name=status" json:"status,omitempty"`                                    // 新状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CAchievementProgress) Reset() {
	*x = S2CAchievementProgress{}
	mi := &file_achievement_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CAchievementProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CAchievementProgress) ProtoMessage() {}

func (x *S2CAchievementProgress) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CAchievementProgress.ProtoReflect.Descriptor instead.
func (*S2CAchievementProgress) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{6}
}

func (x *S2CAchievementProgress) GetAchievementId() uint64 {
	if x != nil && x.AchievementId != nil {
		return *x.AchievementId
	}
	return 0
}

func (x *S2CAchievementProgress) GetProgress() int64 {
	if x != nil && x.Progress != nil {
		return *x.Progress
	}
	return 0
}

func (x *S2CAchievementProgress) GetMaxProgress() int64 {
	if x != nil && x.MaxProgress != nil {
		return *x.MaxProgress
	}
	return 0
}

func (x *S2CAchievementProgress) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

// 成就完成通知
type S2CAchievementComplete struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AchievementId *uint64                `protobuf:"varint,1,opt,name=achievement_id,json=achievementId" json:"achievement_id,omitempty"` // 成就ID
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`                                         // 成就名称
	Rarity        *int32                 `protobuf:"varint,3,opt,name=rarity" json:"rarity,omitempty"`                                    // 稀有度
	Rewards       []*ItemData            `protobuf:"bytes,4,rep,name=rewards" json:"rewards,omitempty"`                                   // 奖励列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CAchievementComplete) Reset() {
	*x = S2CAchievementComplete{}
	mi := &file_achievement_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CAchievementComplete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CAchievementComplete) ProtoMessage() {}

func (x *S2CAchievementComplete) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CAchievementComplete.ProtoReflect.Descriptor instead.
func (*S2CAchievementComplete) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{7}
}

func (x *S2CAchievementComplete) GetAchievementId() uint64 {
	if x != nil && x.AchievementId != nil {
		return *x.AchievementId
	}
	return 0
}

func (x *S2CAchievementComplete) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *S2CAchievementComplete) GetRarity() int32 {
	if x != nil && x.Rarity != nil {
		return *x.Rarity
	}
	return 0
}

func (x *S2CAchievementComplete) GetRewards() []*ItemData {
	if x != nil {
		return x.Rewards
	}
	return nil
}

// 稀缺掉落获得通知
type S2CRareDropObtained struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RareDropId    *uint64                `protobuf:"varint,1,opt,name=rare_drop_id,json=rareDropId" json:"rare_drop_id,omitempty"` // 稀缺掉落ID
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`                                  // 掉落名称
	Rarity        *int32                 `protobuf:"varint,3,opt,name=rarity" json:"rarity,omitempty"`                             // 稀有度
	Source        *string                `protobuf:"bytes,4,opt,name=source" json:"source,omitempty"`                              // 获得来源
	Rewards       []*ItemData            `protobuf:"bytes,5,rep,name=rewards" json:"rewards,omitempty"`                            // 奖励列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CRareDropObtained) Reset() {
	*x = S2CRareDropObtained{}
	mi := &file_achievement_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CRareDropObtained) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CRareDropObtained) ProtoMessage() {}

func (x *S2CRareDropObtained) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CRareDropObtained.ProtoReflect.Descriptor instead.
func (*S2CRareDropObtained) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{8}
}

func (x *S2CRareDropObtained) GetRareDropId() uint64 {
	if x != nil && x.RareDropId != nil {
		return *x.RareDropId
	}
	return 0
}

func (x *S2CRareDropObtained) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *S2CRareDropObtained) GetRarity() int32 {
	if x != nil && x.Rarity != nil {
		return *x.Rarity
	}
	return 0
}

func (x *S2CRareDropObtained) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *S2CRareDropObtained) GetRewards() []*ItemData {
	if x != nil {
		return x.Rewards
	}
	return nil
}

// 搜索游戏请求 10404
type C2SAchievementSearchGame struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Keyword       *string                `protobuf:"bytes,1,opt,name=keyword" json:"keyword,omitempty"` // 搜索关键词
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SAchievementSearchGame) Reset() {
	*x = C2SAchievementSearchGame{}
	mi := &file_achievement_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SAchievementSearchGame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SAchievementSearchGame) ProtoMessage() {}

func (x *C2SAchievementSearchGame) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SAchievementSearchGame.ProtoReflect.Descriptor instead.
func (*C2SAchievementSearchGame) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{9}
}

func (x *C2SAchievementSearchGame) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

// 搜索游戏响应 10405
type S2CAchievementSearchGame struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,opt,name=code,enum=pb.ResponseCode" json:"code,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	Games         []*GameSearchResult    `protobuf:"bytes,3,rep,name=games" json:"games,omitempty"` // 游戏搜索结果
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CAchievementSearchGame) Reset() {
	*x = S2CAchievementSearchGame{}
	mi := &file_achievement_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CAchievementSearchGame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CAchievementSearchGame) ProtoMessage() {}

func (x *S2CAchievementSearchGame) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CAchievementSearchGame.ProtoReflect.Descriptor instead.
func (*S2CAchievementSearchGame) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{10}
}

func (x *S2CAchievementSearchGame) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2CAchievementSearchGame) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *S2CAchievementSearchGame) GetGames() []*GameSearchResult {
	if x != nil {
		return x.Games
	}
	return nil
}

// 游戏搜索结果
type GameSearchResult struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	GameId           *int32                 `protobuf:"varint,1,opt,name=game_id,json=gameId" json:"game_id,omitempty"`                               // 游戏ID
	GameName         *string                `protobuf:"bytes,2,opt,name=game_name,json=gameName" json:"game_name,omitempty"`                          // 游戏名称
	Icon             *string                `protobuf:"bytes,3,opt,name=icon" json:"icon,omitempty"`                                                  // 游戏图标
	AchievementCount *int32                 `protobuf:"varint,4,opt,name=achievement_count,json=achievementCount" json:"achievement_count,omitempty"` // 成就数量
	CompletedCount   *int32                 `protobuf:"varint,5,opt,name=completed_count,json=completedCount" json:"completed_count,omitempty"`       // 已完成数量
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GameSearchResult) Reset() {
	*x = GameSearchResult{}
	mi := &file_achievement_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GameSearchResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameSearchResult) ProtoMessage() {}

func (x *GameSearchResult) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameSearchResult.ProtoReflect.Descriptor instead.
func (*GameSearchResult) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{11}
}

func (x *GameSearchResult) GetGameId() int32 {
	if x != nil && x.GameId != nil {
		return *x.GameId
	}
	return 0
}

func (x *GameSearchResult) GetGameName() string {
	if x != nil && x.GameName != nil {
		return *x.GameName
	}
	return ""
}

func (x *GameSearchResult) GetIcon() string {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return ""
}

func (x *GameSearchResult) GetAchievementCount() int32 {
	if x != nil && x.AchievementCount != nil {
		return *x.AchievementCount
	}
	return 0
}

func (x *GameSearchResult) GetCompletedCount() int32 {
	if x != nil && x.CompletedCount != nil {
		return *x.CompletedCount
	}
	return 0
}

// 成就统计信息
type AchievementStatistics struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	TotalAchievements     *int32                 `protobuf:"varint,1,opt,name=total_achievements,json=totalAchievements" json:"total_achievements,omitempty"`             // 总成就数
	CompletedAchievements *int32                 `protobuf:"varint,2,opt,name=completed_achievements,json=completedAchievements" json:"completed_achievements,omitempty"` // 已完成成就数
	ClaimableAchievements *int32                 `protobuf:"varint,3,opt,name=claimable_achievements,json=claimableAchievements" json:"claimable_achievements,omitempty"` // 可领取成就数
	RareDropsCount        *int32                 `protobuf:"varint,4,opt,name=rare_drops_count,json=rareDropsCount" json:"rare_drops_count,omitempty"`                    // 稀缺掉落数量
	CompletionPercentage  *float32               `protobuf:"fixed32,5,opt,name=completion_percentage,json=completionPercentage" json:"completion_percentage,omitempty"`   // 完成百分比
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *AchievementStatistics) Reset() {
	*x = AchievementStatistics{}
	mi := &file_achievement_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AchievementStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AchievementStatistics) ProtoMessage() {}

func (x *AchievementStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AchievementStatistics.ProtoReflect.Descriptor instead.
func (*AchievementStatistics) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{12}
}

func (x *AchievementStatistics) GetTotalAchievements() int32 {
	if x != nil && x.TotalAchievements != nil {
		return *x.TotalAchievements
	}
	return 0
}

func (x *AchievementStatistics) GetCompletedAchievements() int32 {
	if x != nil && x.CompletedAchievements != nil {
		return *x.CompletedAchievements
	}
	return 0
}

func (x *AchievementStatistics) GetClaimableAchievements() int32 {
	if x != nil && x.ClaimableAchievements != nil {
		return *x.ClaimableAchievements
	}
	return 0
}

func (x *AchievementStatistics) GetRareDropsCount() int32 {
	if x != nil && x.RareDropsCount != nil {
		return *x.RareDropsCount
	}
	return 0
}

func (x *AchievementStatistics) GetCompletionPercentage() float32 {
	if x != nil && x.CompletionPercentage != nil {
		return *x.CompletionPercentage
	}
	return 0
}

// 获取成就统计请求 10406
type C2SAchievementStatistics struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *C2SAchievementStatistics) Reset() {
	*x = C2SAchievementStatistics{}
	mi := &file_achievement_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *C2SAchievementStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2SAchievementStatistics) ProtoMessage() {}

func (x *C2SAchievementStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2SAchievementStatistics.ProtoReflect.Descriptor instead.
func (*C2SAchievementStatistics) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{13}
}

// 获取成就统计响应 10407
type S2CAchievementStatistics struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          *ResponseCode          `protobuf:"varint,1,opt,name=code,enum=pb.ResponseCode" json:"code,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	Statistics    *AchievementStatistics `protobuf:"bytes,3,opt,name=statistics" json:"statistics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S2CAchievementStatistics) Reset() {
	*x = S2CAchievementStatistics{}
	mi := &file_achievement_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S2CAchievementStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2CAchievementStatistics) ProtoMessage() {}

func (x *S2CAchievementStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_achievement_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2CAchievementStatistics.ProtoReflect.Descriptor instead.
func (*S2CAchievementStatistics) Descriptor() ([]byte, []int) {
	return file_achievement_proto_rawDescGZIP(), []int{14}
}

func (x *S2CAchievementStatistics) GetCode() ResponseCode {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ResponseCode_normal
}

func (x *S2CAchievementStatistics) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *S2CAchievementStatistics) GetStatistics() *AchievementStatistics {
	if x != nil {
		return x.Statistics
	}
	return nil
}

var File_achievement_proto protoreflect.FileDescriptor

const file_achievement_proto_rawDesc = "" +
	"\n" +
	"\x11achievement.proto\x12\x02pb\x1a\x10gameconfig.proto\"\xa3\x03\n" +
	"\x0fAchievementInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x17\n" +
	"\agame_id\x18\x04 \x01(\x05R\x06gameId\x12\x1b\n" +
	"\tgame_name\x18\x05 \x01(\tR\bgameName\x12\x12\n" +
	"\x04icon\x18\x06 \x01(\tR\x04icon\x12\x16\n" +
	"\x06rarity\x18\a \x01(\x05R\x06rarity\x12\x16\n" +
	"\x06status\x18\b \x01(\x05R\x06status\x12\x1a\n" +
	"\bprogress\x18\t \x01(\x03R\bprogress\x12!\n" +
	"\fmax_progress\x18\n" +
	" \x01(\x03R\vmaxProgress\x12!\n" +
	"\fcompleted_at\x18\v \x01(\x03R\vcompletedAt\x12\x1d\n" +
	"\n" +
	"claimed_at\x18\f \x01(\x03R\tclaimedAt\x12'\n" +
	"\x0fcompletion_rate\x18\r \x01(\x02R\x0ecompletionRate\x12&\n" +
	"\arewards\x18\x0e \x03(\v2\f.pb.ItemDataR\arewards\"\xe1\x01\n" +
	"\fRareDropInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x12\n" +
	"\x04icon\x18\x04 \x01(\tR\x04icon\x12\x16\n" +
	"\x06rarity\x18\x05 \x01(\x05R\x06rarity\x12\x1f\n" +
	"\vobtained_at\x18\x06 \x01(\x03R\n" +
	"obtainedAt\x12\x16\n" +
	"\x06source\x18\a \x01(\tR\x06source\x12&\n" +
	"\arewards\x18\b \x03(\v2\f.pb.ItemDataR\arewards\"\xa0\x01\n" +
	"\x12C2SAchievementList\x12\x19\n" +
	"\btab_type\x18\x01 \x01(\x05R\atabType\x12\x17\n" +
	"\agame_id\x18\x02 \x01(\x05R\x06gameId\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\x12%\n" +
	"\x0esearch_keyword\x18\x05 \x01(\tR\rsearchKeyword\"\xa4\x02\n" +
	"\x12S2CAchievementList\x12%\n" +
	"\x04code\x18\x01 \x01(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x127\n" +
	"\fachievements\x18\x03 \x03(\v2\x13.pb.AchievementInfoR\fachievements\x12/\n" +
	"\n" +
	"rare_drops\x18\x04 \x03(\v2\x10.pb.RareDropInfoR\trareDrops\x12\x1f\n" +
	"\vtotal_count\x18\x05 \x01(\x05R\n" +
	"totalCount\x12!\n" +
	"\fcurrent_page\x18\x06 \x01(\x05R\vcurrentPage\x12\x1f\n" +
	"\vtotal_pages\x18\a \x01(\x05R\n" +
	"totalPages\"<\n" +
	"\x13C2SAchievementClaim\x12%\n" +
	"\x0eachievement_id\x18\x01 \x01(\x04R\rachievementId\"\xa5\x01\n" +
	"\x13S2CAchievementClaim\x12%\n" +
	"\x04code\x18\x01 \x01(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12%\n" +
	"\x0eachievement_id\x18\x03 \x01(\x04R\rachievementId\x12&\n" +
	"\arewards\x18\x04 \x03(\v2\f.pb.ItemDataR\arewards\"\x96\x01\n" +
	"\x16S2CAchievementProgress\x12%\n" +
	"\x0eachievement_id\x18\x01 \x01(\x04R\rachievementId\x12\x1a\n" +
	"\bprogress\x18\x02 \x01(\x03R\bprogress\x12!\n" +
	"\fmax_progress\x18\x03 \x01(\x03R\vmaxProgress\x12\x16\n" +
	"\x06status\x18\x04 \x01(\x05R\x06status\"\x93\x01\n" +
	"\x16S2CAchievementComplete\x12%\n" +
	"\x0eachievement_id\x18\x01 \x01(\x04R\rachievementId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06rarity\x18\x03 \x01(\x05R\x06rarity\x12&\n" +
	"\arewards\x18\x04 \x03(\v2\f.pb.ItemDataR\arewards\"\xa3\x01\n" +
	"\x13S2CRareDropObtained\x12 \n" +
	"\frare_drop_id\x18\x01 \x01(\x04R\n" +
	"rareDropId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06rarity\x18\x03 \x01(\x05R\x06rarity\x12\x16\n" +
	"\x06source\x18\x04 \x01(\tR\x06source\x12&\n" +
	"\arewards\x18\x05 \x03(\v2\f.pb.ItemDataR\arewards\"4\n" +
	"\x18C2SAchievementSearchGame\x12\x18\n" +
	"\akeyword\x18\x01 \x01(\tR\akeyword\"\x87\x01\n" +
	"\x18S2CAchievementSearchGame\x12%\n" +
	"\x04code\x18\x01 \x01(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12*\n" +
	"\x05games\x18\x03 \x03(\v2\x14.pb.GameSearchResultR\x05games\"\xb2\x01\n" +
	"\x10GameSearchResult\x12\x17\n" +
	"\agame_id\x18\x01 \x01(\x05R\x06gameId\x12\x1b\n" +
	"\tgame_name\x18\x02 \x01(\tR\bgameName\x12\x12\n" +
	"\x04icon\x18\x03 \x01(\tR\x04icon\x12+\n" +
	"\x11achievement_count\x18\x04 \x01(\x05R\x10achievementCount\x12'\n" +
	"\x0fcompleted_count\x18\x05 \x01(\x05R\x0ecompletedCount\"\x93\x02\n" +
	"\x15AchievementStatistics\x12-\n" +
	"\x12total_achievements\x18\x01 \x01(\x05R\x11totalAchievements\x125\n" +
	"\x16completed_achievements\x18\x02 \x01(\x05R\x15completedAchievements\x125\n" +
	"\x16claimable_achievements\x18\x03 \x01(\x05R\x15claimableAchievements\x12(\n" +
	"\x10rare_drops_count\x18\x04 \x01(\x05R\x0erareDropsCount\x123\n" +
	"\x15completion_percentage\x18\x05 \x01(\x02R\x14completionPercentage\"\x1a\n" +
	"\x18C2SAchievementStatistics\"\x96\x01\n" +
	"\x18S2CAchievementStatistics\x12%\n" +
	"\x04code\x18\x01 \x01(\x0e2\x11.pb.response_codeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x129\n" +
	"\n" +
	"statistics\x18\x03 \x01(\v2\x19.pb.AchievementStatisticsR\n" +
	"statistics*5\n" +
	"\rresponse_code\x12\n" +
	"\n" +
	"\x06normal\x10\x00\x12\b\n" +
	"\x04fail\x10\x01\x12\x0e\n" +
	"\n" +
	"params_err\x10dB&Z$kairo_paradise_server/services/pb;pb"

var (
	file_achievement_proto_rawDescOnce sync.Once
	file_achievement_proto_rawDescData []byte
)

func file_achievement_proto_rawDescGZIP() []byte {
	file_achievement_proto_rawDescOnce.Do(func() {
		file_achievement_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_achievement_proto_rawDesc), len(file_achievement_proto_rawDesc)))
	})
	return file_achievement_proto_rawDescData
}

var file_achievement_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_achievement_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_achievement_proto_goTypes = []any{
	(ResponseCode)(0),                // 0: pb.response_code
	(*AchievementInfo)(nil),          // 1: pb.AchievementInfo
	(*RareDropInfo)(nil),             // 2: pb.RareDropInfo
	(*C2SAchievementList)(nil),       // 3: pb.C2SAchievementList
	(*S2CAchievementList)(nil),       // 4: pb.S2CAchievementList
	(*C2SAchievementClaim)(nil),      // 5: pb.C2SAchievementClaim
	(*S2CAchievementClaim)(nil),      // 6: pb.S2CAchievementClaim
	(*S2CAchievementProgress)(nil),   // 7: pb.S2CAchievementProgress
	(*S2CAchievementComplete)(nil),   // 8: pb.S2CAchievementComplete
	(*S2CRareDropObtained)(nil),      // 9: pb.S2CRareDropObtained
	(*C2SAchievementSearchGame)(nil), // 10: pb.C2SAchievementSearchGame
	(*S2CAchievementSearchGame)(nil), // 11: pb.S2CAchievementSearchGame
	(*GameSearchResult)(nil),         // 12: pb.GameSearchResult
	(*AchievementStatistics)(nil),    // 13: pb.AchievementStatistics
	(*C2SAchievementStatistics)(nil), // 14: pb.C2SAchievementStatistics
	(*S2CAchievementStatistics)(nil), // 15: pb.S2CAchievementStatistics
	(*ItemData)(nil),                 // 16: pb.ItemData
}
var file_achievement_proto_depIdxs = []int32{
	16, // 0: pb.AchievementInfo.rewards:type_name -> pb.ItemData
	16, // 1: pb.RareDropInfo.rewards:type_name -> pb.ItemData
	0,  // 2: pb.S2CAchievementList.code:type_name -> pb.response_code
	1,  // 3: pb.S2CAchievementList.achievements:type_name -> pb.AchievementInfo
	2,  // 4: pb.S2CAchievementList.rare_drops:type_name -> pb.RareDropInfo
	0,  // 5: pb.S2CAchievementClaim.code:type_name -> pb.response_code
	16, // 6: pb.S2CAchievementClaim.rewards:type_name -> pb.ItemData
	16, // 7: pb.S2CAchievementComplete.rewards:type_name -> pb.ItemData
	16, // 8: pb.S2CRareDropObtained.rewards:type_name -> pb.ItemData
	0,  // 9: pb.S2CAchievementSearchGame.code:type_name -> pb.response_code
	12, // 10: pb.S2CAchievementSearchGame.games:type_name -> pb.GameSearchResult
	0,  // 11: pb.S2CAchievementStatistics.code:type_name -> pb.response_code
	13, // 12: pb.S2CAchievementStatistics.statistics:type_name -> pb.AchievementStatistics
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_achievement_proto_init() }
func file_achievement_proto_init() {
	if File_achievement_proto != nil {
		return
	}
	file_gameconfig_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_achievement_proto_rawDesc), len(file_achievement_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_achievement_proto_goTypes,
		DependencyIndexes: file_achievement_proto_depIdxs,
		EnumInfos:         file_achievement_proto_enumTypes,
		MessageInfos:      file_achievement_proto_msgTypes,
	}.Build()
	File_achievement_proto = out.File
	file_achievement_proto_goTypes = nil
	file_achievement_proto_depIdxs = nil
}
