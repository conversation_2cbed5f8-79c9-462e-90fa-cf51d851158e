package connection

import (
	"context"
	"errors"
	"github.com/golang/protobuf/proto"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/common/protobuf"
	"kairo_paradise_server/internal/discovery"
	"kairo_paradise_server/internal/grpc"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/gate/internal/handlers"
	"kairo_paradise_server/services/pb"
	"kairo_paradise_server/services/pb/msg"
	"sync"
	"sync/atomic"
	"time"
)

const (
	// Time allowed to write a message to the peer
	writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer
	pongWait = 5 * 60 * time.Second

	// Send pings to peer with this period. Must be less than pongWait
	pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer
	maxMessageSize = 512 * 1024 // 512KB

	idleTimeout = 5 * time.Minute
)

var (
	newline = []byte{'\n'}
	space   = []byte{' '}
)

// Client represents a single WebSocket connection
type Client struct {
	UID           uint64
	conn          *websocket.Conn
	send          chan []byte
	mu            sync.Mutex
	gRPC          map[discovery.ServiceType]*grpc.Client
	closed        int32
	lastActivity  time.Time  // 最后活动时间
	activityMutex sync.Mutex // 保护lastActivity的互斥锁
	done          chan struct{}
}

// NewClient creates a new client connection
func NewClient(conn *websocket.Conn, token string) (*Client, error) {
	claims, err := bootstrap.JwtService.ValidateToken(token)
	if err != nil {
		return nil, errors.New("invalid token")
	}
	// 初始化gRPC服务连接客户端
	gRPCServices := make(map[discovery.ServiceType]*grpc.Client, len(claims.Services)-1)
	for serviceName, addr := range claims.Services {
		if serviceName == string(discovery.ServiceTypeGateGRPC) {
			continue
		}
		// 使用NewClientWithUID创建带有UID的gRPC客户端
		gRPCConn, err := grpc.NewClientWithUID(addr, claims.Uid)
		if err != nil {
			return nil, err
		}
		// 初始化gRPC客户端，并初始化服务列表
		if err := gRPCConn.InitRpc(claims.Services, claims.ChannelNo, claims.PlatformId); err != nil {
			logger.Error("Failed to init gRPC client", zap.Error(err))
		}
		gRPCServices[discovery.ServiceType(serviceName)] = gRPCConn
	}
	if gRPCServices == nil || len(gRPCServices) == 0 {
		return nil, errors.New("gRPC services not available")
	}

	_ = conn.SetReadDeadline(time.Now().Add(idleTimeout))
	// Create new client
	client := &Client{
		UID:          claims.Uid,
		conn:         conn,
		send:         make(chan []byte, 256),
		gRPC:         gRPCServices,
		lastActivity: time.Now(),
		done:         make(chan struct{}),
	}

	return client, nil
}

func (c *Client) Ticker() {
	defer func() {
		_ = c.Close()
	}()
	// 创建一个定时器，用于检查客户端是否超过5分钟没有活动
	inactivityCheckTicker := time.NewTicker(1 * time.Minute)
	defer inactivityCheckTicker.Stop()
	for {
		select {
		case <-inactivityCheckTicker.C:
			// 检查客户端是否超过5分钟没有活动
			if c.IsInactive(idleTimeout) {
				logger.Warn("Client inactive for too long, closing connection", zap.Uint64("client_id", c.UID))
				return
			}
		case <-c.done:
			return
		}
	}
}

// ReadPump pumps messages from the WebSocket connection to the hub
func (c *Client) ReadPump() {
	defer func() {
		_ = c.Close()
	}()

	c.conn.SetReadLimit(maxMessageSize)
	_ = c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		return c.conn.SetReadDeadline(time.Now().Add(pongWait))
	})

	for {
		_, data, err := c.conn.ReadMessage()
		if err != nil {
			logger.Warn("Error reading message", zap.Error(err))
			return
		}
		// 检查消息长度
		if len(data) < 4 {
			return
		}
		c.UpdateActivity()
		_ = c.handleMessage(data)
	}
}

// WritePump pumps messages from the hub to the WebSocket connection
func (c *Client) WritePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		_ = c.Close()
	}()

	for {
		select {
		case bytes, ok := <-c.send:
			// 测试通道阻塞
			//time.Sleep(2 * time.Second)
			_ = c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// The hub closed the channel
				_ = c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.BinaryMessage)
			if err != nil {
				return
			}
			_, _ = w.Write(bytes)

			// Add queued messages to the current websocket bytes
			n := len(c.send)
			for i := 0; i < n; i++ {
				_, _ = w.Write(newline)
				_, _ = w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}
		case <-ticker.C:
			_ = c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage processes incoming messages
// data []byte 由三部分组成，前面2个byte为消息ID，第2~4个byte为rpcID，后面的byte为消息内容
func (c *Client) handleMessage(data []byte) error {
	// 解析消息ID和rpcID
	msgId, rpcId, err := protobuf.GetMsgId(data, false)
	if err != nil {
		c.sendErrorResponse(0, 0, pb.ResponseCode_fail, "Failed to get message ID: "+err.Error())
		return err
	}

	// 记录日志，但在生产环境中可以考虑关闭或降低日志级别
	if logger.IsDebugEnabled() {
		logger.Debug("Received message", zap.Uint32("message_id", msgId), zap.Uint16("rpc_id", rpcId), zap.Int("payload_size", len(data)-4))
	}

	// 提取消息体
	payload := data[4:]
	// 创建上下文并设置超时
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	var resp *pb.S2GRequest
	// 根据消息ID范围路由到不同的服务
	if 10000 <= msgId && msgId < 30000 {
		// 转发给游戏服务 (10000-30000)
		gameClient, ok := c.gRPC[discovery.ServiceTypeGame]
		if !ok {
			c.sendErrorResponse(msgId, rpcId, pb.ResponseCode_fail, "Game service not available")
			return errors.New("game service not available")
		}

		// 使用自动添加上下文数据的gRPC客户端
		resp, err = gameClient.G2SRpc(ctx, &pb.G2SRequest{
			ProtocolId: &msgId,
			Payload:    payload,
		})
	} else if 30000 <= msgId && msgId < 40000 {
		// 转发给公共服务 (30000-40000)
		chatClient, ok := c.gRPC[discovery.ServiceTypePublic]
		if !ok {
			c.sendErrorResponse(msgId, rpcId, pb.ResponseCode_fail, "Chat service not available")
			return errors.New("chat service not available")
		}
		// 使用自动添加上下文数据的gRPC客户端
		resp, err = chatClient.G2SRpc(ctx, &pb.G2SRequest{
			ProtocolId: &msgId,
			Payload:    payload,
		})
	} else {
		// 处理网关接收的消息，则直接返回
		resp, err = handlers.HandleMessage(ctx, msgId, payload)
	}
	// 检查错误
	if err != nil {
		c.sendErrorResponse(msgId, rpcId, pb.ResponseCode_fail, "Failed to process message: "+err.Error())
		return err
	}
	if resp == nil || resp.Payload == nil {
		c.sendErrorResponse(msgId, rpcId, pb.ResponseCode_fail, "Received nil response")
		return err
	}
	// 将rpcID插入到响应中,发送响应
	c.SendMessage(protobuf.MarshalBytes(resp.Payload, rpcId))
	return nil
}

// UpdateActivity 更新客户端最后活动时间
func (c *Client) UpdateActivity() {
	c.activityMutex.Lock()
	c.lastActivity = time.Now()
	c.activityMutex.Unlock()
}

// IsInactive 检查客户端是否超过指定时间没有活动
func (c *Client) IsInactive(timeout time.Duration) bool {
	c.activityMutex.Lock()
	defer c.activityMutex.Unlock()
	return time.Since(c.lastActivity) > timeout
}

// SendMessage sends a message to the client
func (c *Client) SendMessage(msg []byte) {
	if atomic.LoadInt32(&c.closed) == 1 {
		// 客户端已关闭，不发送消息，这里用到原子操作，防止并发时造成往已关闭的channel中写数据导致panic
		logger.Warn("Client closed, not sending message", zap.Uint64("client_id", c.UID))
		return
	}

	// 更新最后活动时间
	c.UpdateActivity()

	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()
	select {
	case c.send <- msg:
		// 消息成功发送到通道
		if logger.IsDebugEnabled() {
			logger.Debug("Message queued for sending", zap.Uint64("client_id", c.UID), zap.Int("message_size", len(msg)))
		}
	case <-ctx.Done():
		logger.Warn("Send timeout, closing connection", zap.Uint64("client_id", c.UID))
		_ = c.Close()
	default:
		// 通道已满，注销客户端
		logger.Warn("Send channel full, closing connection", zap.Uint64("client_id", c.UID))
		_ = c.Close()
	}
}

func (c *Client) sendErrorResponse(msgId uint32, rpcId uint16, code pb.ResponseCode, message string) {
	logger.Error(message, zap.Uint32("message_id", msgId), zap.Uint16("rpc_id", rpcId))
	errMsg := &pb.G2CError{
		Code:       &code,
		Message:    &message,
		ProtocolId: &msgId,
	}
	data, err := protobuf.Marshal(uint16(msg.PCK_G2CError), rpcId, errMsg)
	if err != nil {
		logger.Error("Failed to marshal error response", zap.Error(err))
		return
	}
	c.SendMessage(data.Data)
}

func (c *Client) Close() error {
	// 只有当状态从0变为1时，才执行注销操作
	if atomic.CompareAndSwapInt32(&c.closed, 0, 1) {
		// 关闭gRPC连接，否则会有协程泄露
		for _, client := range c.gRPC {
			b, _ := proto.Marshal(&pb.G2SDisconnect{})
			// 通知下游服务器，断开连接
			_, _ = client.G2SRpc(context.Background(), &pb.G2SRequest{
				ProtocolId: proto.Uint32(uint32(msg.PCK_G2SDisconnect)),
				Payload:    b,
			})
			if err := client.Close(); err != nil {
				logger.Error("Failed to close gRPC connection", zap.Error(err))
			}
		}
		// 注销客户端，这里不关闭channel，让Manager负责关闭channel
		Managers.Unregister(c)
		_ = c.conn.Close()
	}
	return nil
}
