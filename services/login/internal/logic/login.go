package logic

import (
	"errors"
	"fmt"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/common/encrypt"
	"kairo_paradise_server/internal/config"
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/discovery"
	"kairo_paradise_server/internal/jwt"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/sdk_platform"
	"kairo_paradise_server/internal/sdk_platform/leiting"
	"kairo_paradise_server/internal/time_manager"
	"kairo_paradise_server/services/login/internal/model"
)

func sdkLogin(params model.OauthReq) (*sdk_platform.LoginResponse, error) {
	//var s sdk_platform.SdkPlatform
	//switch params.ChannelNo {
	//case leiting.Code:
	//	s = &leiting.LeitingSdkPlatform{}
	//default:
	//	return nil, errors.New("channelNo not found")
	//}
	s := &leiting.LeitingSdkPlatform{}
	data := sdk_platform.LoginParams{
		ChannelNo: params.ChannelNo,
		Game:      consts.Game,
		Token:     params.Token,
		UserId:    params.UserId,
	}
	resp, err := s.Login(data)
	fmt.Printf("sdk 返回: %+v\n", resp)
	if err != nil || resp.Status != 0 {
		return nil, errors.New("authentication failed")
	}
	return resp, nil
}

// LoginSdk 平台授权登录
func LoginSdk(params model.OauthReq) (*model.LoginRes, error) {
	resp, err := sdkLogin(params)
	if err != nil {
		return nil, err
	}
	// todo 这里是否需要加一层缓存层？
	info, err := registerOrFind(oauthIn{
		UserId:    params.UserId,
		ChannelNo: params.ChannelNo,
		Auth:      resp.Data.Auth,
		Age:       resp.Data.Age,
		BirthDay:  resp.Data.BirthDay,
		Ip:        params.Ip,
	})
	if err != nil {
		return nil, err
	}
	tokenPair, err := bootstrap.JwtService.GenerateTokenPair(jwt.LoginClaims{
		Uid:        info.UserId,
		UserType:   info.UserType,
		ChannelNo:  params.ChannelNo,
		PlatformId: int32(1),
		Services:   discovery.GetServices(),
	})
	if err != nil {
		logger.Errorf("generate token pair error: %v", err)
		return nil, err
	}
	// 登录完成记录refresh_token
	if err = setRefreshToken(tokenPair.RefreshToken); err != nil {
		logger.Errorf("save refresh_token err: %v", err)
	}
	expiresIn := time_manager.GlobalTimeManager.Now().Unix() + int64(config.Config.JWTConf.AccessTokenExpiry*60)
	return &model.LoginRes{
		Uid:          info.UserId,
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresIn:    uint(expiresIn),
		Gateway:      discovery.GetGateWay(),
	}, err
}

// Login 账号密码登录，正式环境不可用
func Login(params model.LoginReq) (*model.LoginRes, error) {
	info, err := registerOrFindAccount(loginIn{
		Account:  params.Account,
		Password: params.Password,
		Ip:       params.Ip,
	})
	if err != nil {
		return nil, err
	}
	if !info.IsRegister && info.Password != encrypt.Md5(params.Password+info.Salt) {
		return nil, errors.New("password error")
	}
	tokenPair, err := bootstrap.JwtService.GenerateTokenPair(jwt.LoginClaims{
		Uid:        info.UserId,
		UserType:   info.UserType,
		ChannelNo:  "robot",
		PlatformId: int32(0),
		Services:   discovery.GetServices(),
	})
	if err != nil {
		logger.Errorf("generate token pair error: %v", err)
		return nil, err
	}
	// 登录完成记录refresh_token
	if err = setRefreshToken(tokenPair.RefreshToken); err != nil {
		logger.Errorf("save refresh_token err: %v", err)
	}
	expiresIn := time_manager.GlobalTimeManager.Now().Unix() + int64(config.Config.JWTConf.AccessTokenExpiry*60)
	return &model.LoginRes{
		Uid:          info.UserId,
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresIn:    uint(expiresIn),
		Gateway:      discovery.GetGateWay(),
	}, err
}
