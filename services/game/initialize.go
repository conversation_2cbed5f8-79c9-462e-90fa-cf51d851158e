package game

import (
	"context"
	"google.golang.org/grpc"
	"kairo_paradise_server/internal/config"
	"kairo_paradise_server/internal/discovery"
	"kairo_paradise_server/internal/game_config"
	grpc2 "kairo_paradise_server/services/game/internal/grpc"
	"kairo_paradise_server/services/game/internal/handlers"
	"kairo_paradise_server/services/game/internal/module/player/achievement"
	"kairo_paradise_server/services/game/internal/module/player/init_player"
	"kairo_paradise_server/services/pb"
)

var StatsCollector *discovery.StatsCollector

func Initialize(ctx context.Context) {
	_, _ = discovery.RegisterService(discovery.ServiceTypeGame, config.ServerConf.ServerConfig.Addr, map[string]string{
		"version": "1.0.0",
		"region":  "cn",
	})

	// load game config
	game_config.Initialize()

	// 初始化成就系统
	achievement.GlobalManager.LoadConfigs()

	// Register message handlers
	handlers.RegisterMessageHandlers()

	init_player.Init()
}

func RegisterGRPCServer(server *grpc.Server) {
	pb.RegisterGRpcServiceServer(server, &grpc2.Server{})
}
