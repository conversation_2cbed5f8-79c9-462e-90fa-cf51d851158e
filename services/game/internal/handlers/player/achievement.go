package player

import (
	"context"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/game_config"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/models"
	"kairo_paradise_server/services/game/internal/module/player/achievement"
	"kairo_paradise_server/services/game/internal/module/player_manage"
	"kairo_paradise_server/services/pb"
	"strings"
)

// HandleAchievementList 处理获取成就列表请求
func HandleAchievementList(ctx context.Context, payload []byte) (proto.Message, error) {
	// 解析请求
	request := &pb.C2SAchievementList{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal C2SAchievementList", zap.Error(err))
		return &pb.S2CAchievementList{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("invalid request"),
		}, nil
	}

	// 获取玩家信息
	playerInfo, ok := player_manage.GetPlayerInfoById(ctx, &pb.S2CCommResponse{})
	if !ok {
		return &pb.S2CAchievementList{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("player not found"),
		}, nil
	}

	// 调用成就处理器
	return achievement.GetAchievementList(playerInfo, request)
}

// HandleAchievementClaim 处理领取成就奖励请求
func HandleAchievementClaim(ctx context.Context, payload []byte) (proto.Message, error) {
	// 解析请求
	request := &pb.C2SAchievementClaim{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal C2SAchievementClaim", zap.Error(err))
		return &pb.S2CAchievementClaim{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("invalid request"),
		}, nil
	}

	// 获取玩家信息
	playerInfo, ok := player_manage.GetPlayerInfoById(ctx, &pb.S2CCommResponse{})
	if !ok {
		return &pb.S2CAchievementClaim{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("player not found"),
		}, nil
	}

	// 调用成就处理器
	return achievement.ClaimAchievement(playerInfo, request)
}

// HandleAchievementSearchGame 处理搜索游戏请求
func HandleAchievementSearchGame(ctx context.Context, payload []byte) (proto.Message, error) {
	// 解析请求
	request := &pb.C2SAchievementSearchGame{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal C2SAchievementSearchGame", zap.Error(err))
		return &pb.S2CAchievementSearchGame{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("invalid request"),
		}, nil
	}

	// 获取玩家信息
	playerInfo, ok := player_manage.GetPlayerInfoById(ctx, &pb.S2CCommResponse{})
	if !ok {
		return &pb.S2CAchievementSearchGame{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("player not found"),
		}, nil
	}

	// 调用搜索处理器
	return searchGames(playerInfo, request)
}

// HandleAchievementStatistics 处理获取成就统计请求
func HandleAchievementStatistics(ctx context.Context, payload []byte) (proto.Message, error) {
	// 解析请求
	request := &pb.C2SAchievementStatistics{}
	err := proto.Unmarshal(payload, request)
	if err != nil {
		logger.Error("Failed to unmarshal C2SAchievementStatistics", zap.Error(err))
		return &pb.S2CAchievementStatistics{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("invalid request"),
		}, nil
	}

	// 获取玩家信息
	playerInfo, ok := player_manage.GetPlayerInfoById(ctx, &pb.S2CCommResponse{})
	if !ok {
		return &pb.S2CAchievementStatistics{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("player not found"),
		}, nil
	}

	// 调用统计处理器
	return getAchievementStatistics(playerInfo, request)
}

// searchGames 搜索游戏
func searchGames(p interface{}, request *pb.C2SAchievementSearchGame) (*pb.S2CAchievementSearchGame, error) {
	playerInfo, ok := p.(interface {
		GetPlayerId() uint64
		GetAchievements() *models.PlayerAchievements
	})
	if !ok {
		return &pb.S2CAchievementSearchGame{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("invalid player"),
		}, nil
	}

	keyword := ""
	if request.Keyword != nil {
		keyword = *request.Keyword
	}

	var games []*pb.GameSearchResult

	// 遍历所有游戏配置，查找匹配的游戏
	// TODO: 这里需要实现 GetAll 方法或者使用其他方式遍历游戏配置
	gameIds := []int32{1, 2, 3, 4, 5} // 临时硬编码，实际应该从配置中获取
	for _, gameId := range gameIds {
		gameConfig := game_config.MiniGameConfig.Item(gameId)
		if gameConfig == nil {
			continue
		}
		if gameConfig.Name == nil {
			continue
		}

		gameName := *gameConfig.Name
		if keyword != "" && !strings.Contains(strings.ToLower(gameName), strings.ToLower(keyword)) {
			continue
		}

		// 统计该游戏的成就数量
		totalCount := int32(0)
		completedCount := int32(0)

		achievements := playerInfo.GetAchievements()
		if achievements != nil {
			for _, config := range achievement.GlobalManager.achievements {
				if config.GameId == gameId {
					totalCount++
					if playerAchievement := achievements.Achievements[config.Id]; playerAchievement != nil {
						if playerAchievement.Status >= models.AchievementStatusClaimable {
							completedCount++
						}
					}
				}
			}
		}

		icon := ""
		if gameConfig.Icon != nil {
			icon = *gameConfig.Icon
		}

		games = append(games, &pb.GameSearchResult{
			GameId:           &gameId,
			GameName:         &gameName,
			Icon:             &icon,
			AchievementCount: &totalCount,
			CompletedCount:   &completedCount,
		})
	}

	return &pb.S2CAchievementSearchGame{
		Code:  pb.ResponseCode_normal.Enum(),
		Games: games,
	}, nil
}

// getAchievementStatistics 获取成就统计
func getAchievementStatistics(p interface{}, request *pb.C2SAchievementStatistics) (*pb.S2CAchievementStatistics, error) {
	playerInfo, ok := p.(interface {
		GetPlayerId() uint64
		GetAchievements() *models.PlayerAchievements
	})
	if !ok {
		return &pb.S2CAchievementStatistics{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("invalid player"),
		}, nil
	}

	achievements := playerInfo.GetAchievements()
	if achievements == nil {
		return &pb.S2CAchievementStatistics{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("achievements not loaded"),
		}, nil
	}

	totalAchievements := int32(len(achievement.GlobalManager.achievements))
	completedAchievements := int32(0)
	claimableAchievements := int32(0)
	rareDropsCount := int32(len(achievements.RareDrops))

	for _, playerAchievement := range achievements.Achievements {
		if playerAchievement.Status >= models.AchievementStatusClaimable {
			completedAchievements++
		}
		if playerAchievement.Status == models.AchievementStatusClaimable {
			claimableAchievements++
		}
	}

	completionPercentage := float32(0)
	if totalAchievements > 0 {
		completionPercentage = float32(completedAchievements) / float32(totalAchievements) * 100
	}

	statistics := &pb.AchievementStatistics{
		TotalAchievements:     &totalAchievements,
		CompletedAchievements: &completedAchievements,
		ClaimableAchievements: &claimableAchievements,
		RareDropsCount:        &rareDropsCount,
		CompletionPercentage:  &completionPercentage,
	}

	return &pb.S2CAchievementStatistics{
		Code:       pb.ResponseCode_normal.Enum(),
		Statistics: statistics,
	}, nil
}
