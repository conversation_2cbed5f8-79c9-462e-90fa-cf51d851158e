package player

import (
	"context"
	"github.com/golang/protobuf/proto"
	"kairo_paradise_server/internal/game_config"
	"kairo_paradise_server/internal/models"
	"kairo_paradise_server/internal/time_manager"
	"kairo_paradise_server/services/game/internal/logic/order"
	"kairo_paradise_server/services/game/internal/module/player/achievement"
	"kairo_paradise_server/services/game/internal/module/player/month_card"
	"kairo_paradise_server/services/game/internal/module/player/player_game"
	"kairo_paradise_server/services/game/internal/module/player_manage"
	"kairo_paradise_server/services/game/internal/utils"
	"kairo_paradise_server/services/pb"
)

func HandleStartGame(ctx context.Context, payload []byte) (proto.Message, error) {
	resp := &pb.S2CCommResponse{Code: pb.ResponseCode_fail.Enum()}
	request := &pb.C2SGameCenterStartMiniGame{}
	if err := proto.Unmarshal(payload, request); err != nil {
		resp.Message = proto.String("unmarshal error")
		return resp, err
	}
	playerInfo, ok := player_manage.GetPlayerInfoById(ctx, resp)
	if !ok {
		return resp, nil
	}
	player_game.StartGame(playerInfo.GetGames(), *request.MiniGameId)

	return &pb.S2CCommResponse{Code: pb.ResponseCode_normal.Enum()}, nil
}

func HandleStopGame(ctx context.Context, payload []byte) (proto.Message, error) {
	resp := &pb.S2CCommResponse{Code: pb.ResponseCode_fail.Enum()}
	request := &pb.C2SGameCenterEndMiniGame{}
	if err := proto.Unmarshal(payload, request); err != nil {
		resp.Message = proto.String("unmarshal error")
		return resp, err
	}
	playerInfo, ok := player_manage.GetPlayerInfoById(ctx, resp)
	if !ok {
		return resp, nil
	}
	player_game.StopGame(playerInfo.GetGames())

	return &pb.S2CCommResponse{Code: pb.ResponseCode_normal.Enum()}, nil
}

func HandleBuyGame(ctx context.Context, payload []byte) (proto.Message, error) {
	resp := &pb.S2CGameCenterBuyMiniGame{
		Code: pb.ResponseCode_fail.Enum(),
	}
	request := &pb.C2SGameCenterBuyMiniGame{}
	if err := proto.Unmarshal(payload, request); err != nil {
		resp.Message = proto.String("unmarshal error")
		return resp, err
	}
	uid := utils.GetUIDFromContext(ctx)
	playerInfo, ok := player_manage.Manager.GetPlayerByUid(uid)
	if !ok {
		resp.Message = proto.String("player not found")
		return resp, nil
	}
	gameInfo := game_config.MiniGameConfig.Item(*request.MiniGameId)
	if gameInfo == nil {
		resp.Message = proto.String("game not found")
		return resp, nil
	}
	// 判断是否已经购买
	if player_game.ExistsGame(playerInfo.GetGames(), *request.MiniGameId) {
		resp.Message = proto.String("game already bought")
		return resp, nil
	}

	// 如果是免费游戏，直接添加；
	// 如果是付费游戏应该返回是付费游戏，返回需要下单支付，客户端需要拉起下单
	// 如果是已购买，则需要判断之前是否是免费试玩
	// 如果是月卡用户，判断是否是试玩游戏，如果是则添加，但需要添加过期时间限制

	if gameInfo.IsFree != nil && *gameInfo.IsFree == 1 {
		if err := player_game.AddGame(playerInfo.GetGames(), &models.AddPlayerGame{
			PlayerId: playerInfo.GetPlayerId(),
			GameId:   *request.MiniGameId,
			BuyTime:  time_manager.GlobalTimeManager.Now().Unix(),
			Expire:   0,
		}); err != nil {
			resp.Message = proto.String("add game error")
			return resp, nil
		}
		return &pb.S2CGameCenterBuyMiniGame{
			Code: pb.ResponseCode_normal.Enum(),
			MiniGame: &pb.MiniGameInfo{
				Id:         proto.Int32(*request.MiniGameId),
				ExpireTime: proto.Int64(0),
				BuyTime:    proto.Int64(time_manager.GlobalTimeManager.Now().Unix()),
				GameTime:   proto.Int64(0),
			},
			IsBuy: proto.Bool(true),
		}, nil
	}
	// 处理月卡用户的试玩逻辑
	if month_card.IsMonthlyCardValid(playerInfo) && month_card.CanPlayTrialGame(playerInfo, *request.MiniGameId) {
		// 月卡用户可以试玩付费游戏，添加试玩时间限制
		trialExpireTime := month_card.GetTrialGameExpireTime()
		if err := player_game.AddGame(playerInfo.GetGames(), &models.AddPlayerGame{
			PlayerId: playerInfo.GetPlayerId(),
			GameId:   *request.MiniGameId,
			BuyTime:  time_manager.GlobalTimeManager.Now().Unix(),
			Expire:   trialExpireTime,
		}); err != nil {
			resp.Message = proto.String("add trial game error")
			return resp, nil
		}
		return &pb.S2CGameCenterBuyMiniGame{
			Code: pb.ResponseCode_normal.Enum(),
			MiniGame: &pb.MiniGameInfo{
				Id:         proto.Int32(*request.MiniGameId),
				ExpireTime: proto.Int64(time_manager.GlobalTimeManager.Now().Unix() + trialExpireTime),
				BuyTime:    proto.Int64(time_manager.GlobalTimeManager.Now().Unix()),
				GameTime:   proto.Int64(0),
			},
			IsBuy: proto.Bool(true),
		}, nil
	}
	// 处理付费游戏，需要先下单
	if gameInfo.RechargeId == nil || *gameInfo.RechargeId == 0 {
		resp.Message = proto.String("Missing recharge configuration")
		return resp, nil
	}
	orderId, err := order.CreateGameOrder(playerInfo, gameInfo)
	if err != nil {
		resp.Message = proto.String("Create order error")
		return resp, nil
	}
	return &pb.S2CGameCenterBuyMiniGame{
		Code:    pb.ResponseCode_normal.Enum(),
		OrderId: proto.String(orderId),
		IsBuy:   proto.Bool(false),
	}, nil
}
