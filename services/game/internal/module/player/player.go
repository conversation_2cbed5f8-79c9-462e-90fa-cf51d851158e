package player

import (
	"encoding/json"
	"kairo_paradise_server/internal/discovery"
	"kairo_paradise_server/internal/grpc"
	"kairo_paradise_server/internal/models"
)

func (p *Player) GetPlayerId() uint64 {
	return p.Id
}

func (p *Player) GetUserId() uint64 {
	return p.UserId
}

func (p *Player) GetUserInfo() *models.UserInfo {
	return p.UserInfo
}

func (p *Player) GetPlayerInfo() *models.Player {
	return p.Info
}

func (p *Player) GetAssets() *models.Assets {
	return p.Assets
}

func (p *Player) SetAssets(assets *models.Assets) {
	p.Assets = assets
}

func (p *Player) GetScene() *models.Scene {
	return p.Scene
}

func (p *Player) SetScene(scene *models.Scene) {
	p.Scene = scene
}

func (p *Player) GetMailBox() *models.PlayerMailBox {
	return p.MailBox
}

func (p *Player) SetMailBox(mailBox *models.PlayerMailBox) {
	p.MailBox = mailBox
}

func (p *Player) GetAnnouncementBox() *models.PlayerAnnouncementBox {
	return p.AnnouncementBox
}

func (p *Player) SetAnnouncementBox(announcementBox *models.PlayerAnnouncementBox) {
	p.AnnouncementBox = announcementBox
}

func (p *Player) IsRobot() bool {
	return p.isRobot
}

func (p *Player) IsRegister() bool {
	return p.GetPlayerInfo().Nick == ""
}

func (p *Player) SetUserInfo(userInfo *models.UserInfo) {
	p.UserInfo = userInfo
}

func (p *Player) SetInfo(info *models.Player) {
	p.Id = info.Id
	p.Info = info
}

func (p *Player) SetServices(serviceStr string) {
	_ = json.Unmarshal([]byte(serviceStr), &p.serviceMaps)
	for k, v := range p.serviceMaps {
		if k != discovery.ServiceTypeGateGRPC {
			continue
		}
		p.services[k], _ = grpc.NewClientWithUID(v, p.GetUserId())
	}
}

func (p *Player) SetIsReconnect(isReconnect bool) {
	p.isReconnect = isReconnect
}

func (p *Player) SetGames(games *models.PlayerGames) {
	p.Games = games
}

func (p *Player) GetGames() *models.PlayerGames {
	return p.Games
}

func (p *Player) GetMonthlyCard() *models.MonthlyCard {
	return p.MonthlyCard
}

func (p *Player) SetMonthlyCard(monthlyCard *models.MonthlyCard) {
	p.MonthlyCard = monthlyCard
}

func (p *Player) SetPlatformInfo(platformId int32, channelNo string) {
	p.PlatformId = platformId
	p.ChannelNo = channelNo
}

func (p *Player) GetPlatformInfo() (int32, string) {
	return p.PlatformId, p.ChannelNo
}
