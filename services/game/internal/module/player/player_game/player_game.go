package player_game

import (
	"errors"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"kairo_paradise_server/internal/I"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/models"
	"kairo_paradise_server/internal/time_manager"
	"kairo_paradise_server/services/pb"
)

func LoadPlayerGame(p I.IPlayer) {
	playerId := p.GetPlayerId()
	var list []models.PlayerGame
	err := bootstrap.MysqlDb.Model(&models.PlayerGame{}).Select("game_id, buy_time, expire_time, last_time").Where("player_id = ? AND (expire_time = 0 OR expire_time > ?)", playerId, time_manager.GlobalTimeManager.Now().Unix()).Find(&list).Error
	if err != nil && !errors.As(err, &gorm.ErrRecordNotFound) {
		return
	}
	games := &models.PlayerGames{}
	games.PlayerId = playerId
	games.GameList = make(map[int32]*models.PlayerGame)
	for _, game := range list {
		games.GameList[game.GameId] = &game
	}
	p.SetGames(games)
}

func SavePlayerGame(games *models.PlayerGames) {
	if games == nil {
		return
	}
	for _, game := range games.GameList {
		game.UpdatedAt = time_manager.GlobalTimeManager.Now().Unix()
		err := bootstrap.MysqlDb.Model(&models.PlayerGame{}).Where("player_id = ? AND game_id = ?", games.PlayerId, game.GameId).Updates(game).Error
		if err != nil {
			logger.Error("Failed to save player game", zap.Error(err), zap.Uint64("player_id", game.PlayerId), zap.Int32("game_id", game.GameId))
		}
	}
}

// StartGame 开始游戏
func StartGame(games *models.PlayerGames, gameId int32) {
	if games == nil {
		return
	}
	if _, ok := games.GameList[gameId]; !ok {
		return
	}
	games.CurrentGameId = gameId
	games.StartPlayerGameTime = time_manager.GlobalTimeManager.Now().Unix()
	logger.Info("StartGame", zap.Uint64("player_id", games.PlayerId), zap.Int32("game_id", gameId))
}

// StopGame 停止游戏，更新内存中的数据
func StopGame(games *models.PlayerGames) {
	if games == nil || games.CurrentGameId == 0 {
		return
	}
	game := games.GameList[games.CurrentGameId]
	if game == nil {
		return
	}
	game.LastTime = games.StartPlayerGameTime
	game.GameTime += time_manager.GlobalTimeManager.Now().Unix() - games.StartPlayerGameTime
	games.StartPlayerGameTime = 0
	logger.Info("StopGame", zap.Uint64("player_id", games.PlayerId), zap.Int32("game_id", games.CurrentGameId), zap.Int64("game_time", game.GameTime))
}

// AddGame 购买游戏，也有可能是免费游戏
func AddGame(games *models.PlayerGames, gameEntry *models.AddPlayerGame) error {
	if games == nil {
		return errors.New("games is nil")
	}
	ts := time_manager.GlobalTimeManager.Now().Unix()
	if _, exist := games.GameList[gameEntry.GameId]; !exist {
		m := &models.PlayerGame{
			PlayerId:  gameEntry.PlayerId,
			GameId:    gameEntry.GameId,
			BuyTime:   gameEntry.BuyTime,
			CreatedAt: time_manager.GlobalTimeManager.Now().Unix(),
			UpdatedAt: time_manager.GlobalTimeManager.Now().Unix(),
		}
		if gameEntry.Expire > 0 {
			m.ExpireTime = ts + gameEntry.Expire
		}
		if err := bootstrap.MysqlDb.Create(m).Error; err != nil {
			logger.Error("Failed to add game", zap.Error(err), zap.Uint64("player_id", gameEntry.PlayerId), zap.Int32("game_id", gameEntry.GameId))
			return err
		}
		games.GameList[gameEntry.GameId] = m
	}
	game := games.GameList[gameEntry.GameId]
	game.BuyTime = gameEntry.BuyTime
	game.ExpireTime += gameEntry.Expire
	game.Times += 1
	game.UpdatedAt = ts
	if err := bootstrap.MysqlDb.Model(&models.PlayerGame{}).Where("player_id = ? AND game_id = ?", gameEntry.PlayerId, gameEntry.GameId).Updates(game).Error; err != nil {
		logger.Error("Failed to update game", zap.Error(err), zap.Uint64("player_id", gameEntry.PlayerId), zap.Int32("game_id", gameEntry.GameId))
		return err
	}
	return nil
}

func ExistsGame(games *models.PlayerGames, gameId int32) bool {
	if games == nil {
		return false
	}
	info, exist := games.GameList[gameId]
	return exist && (info.ExpireTime > time_manager.GlobalTimeManager.Now().Unix() || info.ExpireTime == 0)
}

func GameCenterInitMessage(p I.IPlayer) *pb.S2CGameCenterInit {
	list := &pb.S2CGameCenterInit{}
	games := p.GetGames()
	if games == nil {
		return list
	}
	for _, game := range games.GameList {
		list.MiniGames = append(list.MiniGames, &pb.MiniGameInfo{
			Id:         proto.Int32(game.GameId),
			ExpireTime: proto.Int64(game.ExpireTime),
			BuyTime:    proto.Int64(game.BuyTime),
			GameTime:   proto.Int64(game.GameTime),
		})
	}
	return list
}
