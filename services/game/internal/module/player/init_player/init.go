package init_player

import (
	"context"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/I"
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/time_manager"
	"kairo_paradise_server/services/game/internal/module/player"
	"kairo_paradise_server/services/game/internal/module/player/announcement"
	"kairo_paradise_server/services/game/internal/module/player/assets"
	"kairo_paradise_server/services/game/internal/module/player/mail"
	"kairo_paradise_server/services/game/internal/module/player/player_game"
	"kairo_paradise_server/services/game/internal/module/player/scene"
	"kairo_paradise_server/services/game/internal/module/player/user"
	"kairo_paradise_server/services/game/internal/module/player_manage"
	"kairo_paradise_server/services/pb"
	"time"
)

// schedulePlayerCleanup 在玩家断开连接后启动一个定时器，如果30分钟内玩家没有重新连接，则销毁玩家对象并释放内存
func schedulePlayerCleanup(p I.IPlayer) {
	uid := p.GetUserId()
	logger.Info("Scheduling player cleanup after 30 minutes of inactivity", zap.Uint64("uid", uid))

	// 首先取消之前的清理任务（如果有的话）
	p.CleanupCancel()

	// 创建一个可取消的上下文
	ctx, cancel := context.WithCancel(context.Background())
	p.SetCleanupCancel(cancel)

	// 启动一个定时器，30分钟后检查玩家状态
	go func() {
		// 等待30分钟或者上下文被取消
		select {
		case <-time.After(30 * time.Minute):
			// 时间到了，继续执行
			logger.Info("Cleanup timer expired, checking player state", zap.Uint64("uid", uid))
		case <-ctx.Done():
			// 上下文被取消，终止清理任务
			logger.Info("Cleanup task cancelled", zap.Uint64("uid", uid))
			return
		}

		// 获取玩家对象
		playerInfo, ok := player_manage.Manager.GetPlayerByUid(uid)
		if !ok {
			// 玩家已经被移除，无需进一步处理
			logger.Info("Player already removed from manager", zap.Uint64("uid", uid))
			return
		}

		// 检查玩家是否仍然处于离线状态
		if playerInfo.(*player.Player).State == consts.PlayerStateOffLine {
			// 计算玩家离线的时间
			now := time_manager.NewTimeManager().Now().UnixNano() / 1e6
			offlineTime := now - playerInfo.(*player.Player).StateStartTime

			// 如果离线时间超过30分钟，则销毁玩家对象
			if offlineTime >= 30*60*1000 { // 30分钟换算成毫秒
				logger.Info("Player inactive for 30 minutes, destroying",
					zap.Uint64("uid", uid),
					zap.Int64("offline_time_ms", offlineTime))

				// 调用OnDestroy销毁玩家对象
				playerInfo.OnDestroy()
			} else {
				logger.Info("Player offline time not reached 30 minutes yet",
					zap.Uint64("uid", uid),
					zap.Int64("offline_time_ms", offlineTime))
			}
		} else {
			logger.Info("Player is no longer offline, cleanup canceled",
				zap.Uint64("uid", uid),
				zap.Int32("state", int32(playerInfo.(*player.Player).State)))
		}
	}()
}

func Init() {
	// 注册玩家操作的回调事件
	player.LoadFunc = make([]func(p I.IPlayer), 0)
	player.AddLoadFunc(user.LoadUserInfo)
	player.AddLoadFunc(assets.LoadPlayerAssets)
	player.AddLoadFunc(scene.LoadPlayerScene)
	player.AddLoadFunc(mail.LoadPlayerMails)
	player.AddLoadFunc(announcement.LoadPlayerAnnouncements)
	player.AddLoadFunc(player_game.LoadPlayerGame)

	player.AfterLoadFunc = make([]func(p I.IPlayer), 0)
	player.AfterLoadFunc = append(player.AfterLoadFunc, func(p I.IPlayer) {
	})

	// 登录成功回调
	player.OnLoginSuccessFunc = make([]func(p I.IPlayer), 0)
	player.OnLoginSuccessFunc = append(player.OnLoginSuccessFunc, func(p I.IPlayer) {
		// 取消清理任务
		p.CleanupCancel()
	})
	player.OnLoginSuccessFunc = append(player.OnLoginSuccessFunc, func(p I.IPlayer) {
		// 发送玩家协议
		eGender := pb.EGender(p.GetPlayerInfo().Gender)
		p.WriteMsg(&pb.S2CPlayerInfo{
			Code:       pb.ResponseCode_normal.Enum(),
			Icon:       proto.Int32(p.GetPlayerInfo().Icon),
			Nick:       proto.String(p.GetPlayerInfo().Nick),
			Id:         proto.Uint64(p.GetPlayerInfo().Id),
			Level:      proto.Int32(p.GetPlayerInfo().Level),
			Prosperity: proto.Int32(p.GetPlayerInfo().Prosperity),
			Gender:     &eGender,
		})
	})
	player.OnLoginSuccessFunc = append(player.OnLoginSuccessFunc, func(p I.IPlayer) {
		// 发送背包数据
		p.WriteMsg(assets.BackpackInitMessage(p.GetAssets()))
		// 发送游戏列表初始化数据
		p.WriteMsg(player_game.GameCenterInitMessage(p))
		// 发送场景数据
		p.WriteMsg(scene.GetSceneDataApi(p))
		// 发送邮件数据
		p.WriteMsg(mail.GetMailListApi(p))
	})
	// 最后发送login_finish协议
	player.OnLoginSuccessFunc = append(player.OnLoginSuccessFunc, func(p I.IPlayer) {
		p.WriteMsg(&pb.S2CLoginFinish{
			Code: pb.ResponseCode_normal.Enum(),
		})
	})

	// 断开连接回调
	player.OnDisconnectFunc = make([]func(p I.IPlayer), 0)
	// 添加断开连接后30分钟清理玩家数据的逻辑
	player.OnDisconnectFunc = append(player.OnDisconnectFunc, schedulePlayerCleanup)
	player.OnDisconnectFunc = append(player.OnDisconnectFunc, func(p I.IPlayer) {
		// 停止当前游戏的计时
		player_game.StopGame(p.GetGames())
		player_game.SavePlayerGame(p.GetGames())
	})

	// 销毁回调
	player.OnDestroyFunc = make([]func(p I.IPlayer), 0)
	player.OnDestroyFunc = append(player.OnDestroyFunc, func(p I.IPlayer) {
		// 取消清理任务
		p.CleanupCancel()
	})
	player.OnDestroyFunc = append(player.OnDestroyFunc, func(p I.IPlayer) {
		// 添加销毁时保存玩家数据的逻辑
		assets.SaveAssets(p.GetAssets())
		scene.SaveSceneApi(p)
		mail.SavePlayerMails(p.GetMailBox())
	})
	player.OnDestroyFunc = append(player.OnDestroyFunc, func(p I.IPlayer) {
		// 添加销毁时从管理器中移除玩家的逻辑
		player_manage.Manager.RemovePlayer(p)
		logger.Info("Player destroyed and removed from manager", zap.Uint64("uid", p.GetUserId()))
	})
}
