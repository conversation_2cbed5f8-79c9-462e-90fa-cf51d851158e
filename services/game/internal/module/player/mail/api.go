package mail

import (
	"kairo_paradise_server/internal/I"
	"kairo_paradise_server/internal/models"
	"kairo_paradise_server/services/pb"
)

// SendMailApi sends a mail to a player using a template
func SendMailApi(p I.IPlayer, mailId int32, attachments []*pb.ItemData, expireSeconds int64) (*models.Mail, error) {
	mail, err := SendMail(p, mailId, attachments, expireSeconds)
	if err != nil {
		return nil, err
	}

	SavePlayerMails(p.GetMailBox())

	return mail, nil
}

// SendCustomMailApi sends a custom mail to a player
func SendCustomMailApi(p I.IPlayer, title, content, sender string, attachments []*pb.ItemData, expireSeconds int64) (*models.Mail, error) {
	mail, err := SendCustomMail(p, title, content, sender, attachments, expireSeconds)
	if err != nil {
		return nil, err
	}

	SavePlayerMails(p.GetMailBox())

	return mail, nil
}

// ReadMailApi marks a mail as read
func ReadMailApi(p I.IPlayer, mailId uint64) (*pb.MailInfo, error) {
	mail, err := ReadMail(p, mailId)
	if err != nil {
		return nil, err
	}
	return convertMailToProto(mail), nil
}

// ClaimMailAttachmentsApi claims the attachments from a mail
func ClaimMailAttachmentsApi(p I.IPlayer, mailId uint64) error {
	err := ClaimMailAttachments(p, mailId)
	if err != nil {
		return err
	}

	return nil
}

// DeleteMailApi deletes a mail
func DeleteMailApi(p I.IPlayer, mailId uint64) error {
	err := DeleteMail(p, mailId)
	if err != nil {
		return err
	}
	return nil
}

func GetMailListApi(p I.IPlayer) []*pb.MailInfo {
	mails := GetMailList(p)

	// Convert to protocol message
	mailList := make([]*pb.MailInfo, 0, len(mails))
	for _, mail := range mails {
		mailList = append(mailList, convertMailToProto(mail))
	}
	return mailList
}
