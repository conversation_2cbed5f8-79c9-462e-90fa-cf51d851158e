package month_card

import (
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"kairo_paradise_server/internal/I"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/models"
	"kairo_paradise_server/internal/time_manager"
)

// LoadPlayerMonthlyCard 加载玩家月卡信息
func LoadPlayerMonthlyCard(p I.IPlayer) {
	playerId := p.GetPlayerId()

	var dbCard models.PlayerMonthlyCard
	err := bootstrap.MysqlDb.Model(&models.PlayerMonthlyCard{}).
		Where("player_id = ? AND expire_time > ?", playerId, time_manager.GlobalTimeManager.Now().Unix()).
		First(&dbCard).Error

	if err != nil && !errors.As(err, &gorm.ErrRecordNotFound) {
		logger.Error("Failed to load player monthly card", zap.Error(err), zap.Uint64("player_id", playerId))
		return
	}

	if dbCard.Id > 0 {
		monthlyCard := &models.MonthlyCard{
			ExpireTime: dbCard.ExpireTime,
			CardItemId: dbCard.CardItemId,
			Level:      1, // 默认级别，可以根据需要调整
		}
		p.SetMonthlyCard(monthlyCard)
		logger.Debug("Loaded monthly card", zap.Uint64("player_id", playerId), zap.Int64("expire_time", dbCard.ExpireTime))
	}
}

// SavePlayerMonthlyCard 保存玩家月卡信息
func SavePlayerMonthlyCard(p I.IPlayer) {
	monthlyCard := p.GetMonthlyCard()
	if monthlyCard == nil {
		return
	}

	playerId := p.GetPlayerId()

	// 更新数据库中的月卡信息
	err := bootstrap.MysqlDb.Model(&models.PlayerMonthlyCard{}).
		Where("player_id = ? AND card_item_id = ?", playerId, monthlyCard.CardItemId).
		Updates(map[string]interface{}{
			"expire_time": monthlyCard.ExpireTime,
		}).Error

	if err != nil {
		logger.Error("Failed to save player monthly card", zap.Error(err), zap.Uint64("player_id", playerId))
	}
}

// IsMonthlyCardValid 检查月卡是否有效
func IsMonthlyCardValid(p I.IPlayer) bool {
	monthlyCard := p.GetMonthlyCard()
	if monthlyCard == nil {
		return false
	}
	return monthlyCard.HasValid()
}

// GetTrialGameExpireTime 获取试玩游戏的过期时间（月卡用户专用）
// 月卡用户可以试玩付费游戏，试玩时间为7天
func GetTrialGameExpireTime() int64 {
	return 7 * 24 * 60 * 60 // 7天的秒数
}

// CanPlayTrialGame 检查月卡用户是否可以试玩指定游戏
func CanPlayTrialGame(p I.IPlayer, gameId int32) bool {
	// 检查是否是月卡用户
	if !IsMonthlyCardValid(p) {
		return false
	}

	// 检查游戏是否已经拥有（包括试玩版本）
	games := p.GetGames()
	if games == nil {
		return true
	}

	// 如果已经拥有这个游戏，则不能再次试玩
	_, exists := games.GameList[gameId]
	return !exists
}
