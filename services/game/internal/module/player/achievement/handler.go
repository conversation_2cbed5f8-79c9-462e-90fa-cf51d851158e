package achievement

import (
	"context"
	"fmt"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/game_config"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/models"
	"kairo_paradise_server/internal/time_manager"
	"kairo_paradise_server/services/game/internal/module/player_manage"
	"kairo_paradise_server/services/pb"
	"math"
	"strings"
)

// GetAchievementList 获取成就列表
func GetAchievementList(p interface{}, request *pb.C2SAchievementList) (*pb.S2CAchievementList, error) {
	playerInfo, ok := p.(interface {
		GetPlayerId() uint64
		GetAchievements() *models.PlayerAchievements
	})
	if !ok {
		return &pb.S2CAchievementList{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("invalid player"),
		}, nil
	}

	achievements := playerInfo.GetAchievements()
	if achievements == nil {
		return &pb.S2CAchievementList{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("achievements not loaded"),
		}, nil
	}

	// 设置默认值
	tabType := int32(0)
	if request.TabType != nil {
		tabType = *request.TabType
	}

	gameId := int32(0)
	if request.GameId != nil {
		gameId = *request.GameId
	}

	page := int32(1)
	if request.Page != nil && *request.Page > 0 {
		page = *request.Page
	}

	pageSize := int32(20)
	if request.PageSize != nil && *request.PageSize > 0 {
		pageSize = *request.PageSize
	}

	searchKeyword := ""
	if request.SearchKeyword != nil {
		searchKeyword = strings.TrimSpace(*request.SearchKeyword)
	}

	var achievementInfos []*pb.AchievementInfo
	var rareDropInfos []*pb.RareDropInfo

	if tabType == 4 { // 稀缺掉落
		rareDropInfos = buildRareDropList(achievements, searchKeyword)
	} else {
		achievementInfos = buildAchievementList(achievements, tabType, gameId, searchKeyword)
	}

	// 分页处理
	totalCount := int32(len(achievementInfos) + len(rareDropInfos))
	totalPages := int32(math.Ceil(float64(totalCount) / float64(pageSize)))

	startIndex := (page - 1) * pageSize
	endIndex := startIndex + pageSize

	if tabType == 4 {
		if startIndex < int32(len(rareDropInfos)) {
			if endIndex > int32(len(rareDropInfos)) {
				endIndex = int32(len(rareDropInfos))
			}
			rareDropInfos = rareDropInfos[startIndex:endIndex]
		} else {
			rareDropInfos = []*pb.RareDropInfo{}
		}
	} else {
		if startIndex < int32(len(achievementInfos)) {
			if endIndex > int32(len(achievementInfos)) {
				endIndex = int32(len(achievementInfos))
			}
			achievementInfos = achievementInfos[startIndex:endIndex]
		} else {
			achievementInfos = []*pb.AchievementInfo{}
		}
	}

	return &pb.S2CAchievementList{
		Code:         pb.ResponseCode_normal.Enum(),
		Achievements: achievementInfos,
		RareDrops:    rareDropInfos,
		TotalCount:   &totalCount,
		CurrentPage:  &page,
		TotalPages:   &totalPages,
	}, nil
}

// buildAchievementList 构建成就列表
func buildAchievementList(achievements *models.PlayerAchievements, tabType, gameId int32, searchKeyword string) []*pb.AchievementInfo {
	var result []*pb.AchievementInfo

	for achievementId, config := range GlobalManager.achievements {
		// 游戏筛选
		if gameId != 0 && config.GameId != gameId {
			continue
		}

		// 搜索关键词筛选
		if searchKeyword != "" {
			if !strings.Contains(strings.ToLower(config.Name), strings.ToLower(searchKeyword)) &&
				!strings.Contains(strings.ToLower(config.Description), strings.ToLower(searchKeyword)) {
				continue
			}
		}

		playerAchievement := achievements.Achievements[achievementId]
		status := int32(models.AchievementStatusIncomplete)
		progress := int64(0)
		maxProgress := int64(1)
		completedAt := int64(0)
		claimedAt := int64(0)

		if playerAchievement != nil {
			status = playerAchievement.Status
			progress = playerAchievement.Progress
			maxProgress = playerAchievement.MaxProgress
			completedAt = playerAchievement.CompletedAt
			claimedAt = playerAchievement.ClaimedAt
		}

		// 标签筛选
		switch tabType {
		case 1: // 未完成
			if status != models.AchievementStatusIncomplete {
				continue
			}
		case 2: // 已领取
			if status != models.AchievementStatusClaimed {
				continue
			}
		case 3: // 可领取
			if status != models.AchievementStatusClaimable {
				continue
			}
		}

		// 获取游戏名称
		gameName := getGameName(config.GameId)

		// 获取奖励信息
		rewards := getAchievementRewards(config.AwardId)

		// 计算达成百分比（这里简化处理，实际应该从统计数据中获取）
		completionRate := float32(0.1) // 默认值，实际应该从数据库统计

		achievementInfo := &pb.AchievementInfo{
			Id:             &achievementId,
			Name:           &config.Name,
			Description:    &config.Description,
			GameId:         &config.GameId,
			GameName:       &gameName,
			Icon:           &config.Icon,
			Rarity:         &config.Rarity,
			Status:         &status,
			Progress:       &progress,
			MaxProgress:    &maxProgress,
			CompletedAt:    &completedAt,
			ClaimedAt:      &claimedAt,
			CompletionRate: &completionRate,
			Rewards:        rewards,
		}

		result = append(result, achievementInfo)
	}

	// 按稀有度和排序权重排序
	sortAchievements(result)

	return result
}

// buildRareDropList 构建稀缺掉落列表
func buildRareDropList(achievements *models.PlayerAchievements, searchKeyword string) []*pb.RareDropInfo {
	var result []*pb.RareDropInfo

	for rareDropId, playerRareDrop := range achievements.RareDrops {
		config := GlobalManager.rareDrops[rareDropId]
		if config == nil {
			continue
		}

		// 搜索关键词筛选
		if searchKeyword != "" {
			if !strings.Contains(strings.ToLower(config.Name), strings.ToLower(searchKeyword)) &&
				!strings.Contains(strings.ToLower(config.Description), strings.ToLower(searchKeyword)) {
				continue
			}
		}

		// 获取奖励信息
		rewards := getAchievementRewards(config.AwardId)

		rareDropInfo := &pb.RareDropInfo{
			Id:          &rareDropId,
			Name:        &config.Name,
			Description: &config.Description,
			Icon:        &config.Icon,
			Rarity:      &config.Rarity,
			ObtainedAt:  &playerRareDrop.ObtainedAt,
			Source:      &playerRareDrop.Source,
			Rewards:     rewards,
		}

		result = append(result, rareDropInfo)
	}

	// 按获得时间倒序排序
	sortRareDrops(result)

	return result
}

// getGameName 获取游戏名称
func getGameName(gameId int32) string {
	gameConfig := game_config.MiniGameConfig.Item(gameId)
	if gameConfig != nil && gameConfig.Name != nil {
		return *gameConfig.Name
	}
	return fmt.Sprintf("Game_%d", gameId)
}

// getAchievementRewards 获取成就奖励
func getAchievementRewards(awardId int32) []*pb.ItemData {
	var rewards []*pb.ItemData

	awardConfig := game_config.AwardConfig.Item(awardId)
	if awardConfig != nil {
		for _, award := range awardConfig.Award {
			if award.ItemId != nil && award.Count != nil {
				rewards = append(rewards, &pb.ItemData{
					Id:    award.ItemId,
					Count: award.Count,
				})
			}
		}
	}

	return rewards
}

// sortAchievements 排序成就列表
func sortAchievements(achievements []*pb.AchievementInfo) {
	// 简化排序：按稀有度降序，然后按ID升序
	for i := 0; i < len(achievements)-1; i++ {
		for j := i + 1; j < len(achievements); j++ {
			if *achievements[i].Rarity < *achievements[j].Rarity ||
				(*achievements[i].Rarity == *achievements[j].Rarity && *achievements[i].Id > *achievements[j].Id) {
				achievements[i], achievements[j] = achievements[j], achievements[i]
			}
		}
	}
}

// sortRareDrops 排序稀缺掉落列表
func sortRareDrops(rareDrops []*pb.RareDropInfo) {
	// 按获得时间倒序排序
	for i := 0; i < len(rareDrops)-1; i++ {
		for j := i + 1; j < len(rareDrops); j++ {
			if *rareDrops[i].ObtainedAt < *rareDrops[j].ObtainedAt {
				rareDrops[i], rareDrops[j] = rareDrops[j], rareDrops[i]
			}
		}
	}
}

// ClaimAchievement 领取成就奖励
func ClaimAchievement(p interface{}, request *pb.C2SAchievementClaim) (*pb.S2CAchievementClaim, error) {
	playerInfo, ok := p.(interface {
		GetPlayerId() uint64
		GetAchievements() *models.PlayerAchievements
		WriteMsg(interface{})
	})
	if !ok {
		return &pb.S2CAchievementClaim{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("invalid player"),
		}, nil
	}

	if request.AchievementId == nil {
		return &pb.S2CAchievementClaim{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("missing achievement id"),
		}, nil
	}

	achievements := playerInfo.GetAchievements()
	if achievements == nil {
		return &pb.S2CAchievementClaim{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("achievements not loaded"),
		}, nil
	}

	achievementId := *request.AchievementId
	playerAchievement := achievements.Achievements[achievementId]

	if playerAchievement == nil {
		return &pb.S2CAchievementClaim{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("achievement not found"),
		}, nil
	}

	if playerAchievement.Status != models.AchievementStatusClaimable {
		return &pb.S2CAchievementClaim{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("achievement not claimable"),
		}, nil
	}

	// 更新状态
	playerAchievement.Status = models.AchievementStatusClaimed
	playerAchievement.ClaimedAt = time_manager.GlobalTimeManager.Now().Unix()
	playerAchievement.UpdatedAt = time_manager.GlobalTimeManager.Now().Unix()

	// 获取奖励配置
	config := GlobalManager.achievements[achievementId]
	if config == nil {
		return &pb.S2CAchievementClaim{
			Code:    pb.ResponseCode_error.Enum(),
			Message: proto.String("achievement config not found"),
		}, nil
	}

	rewards := getAchievementRewards(config.AwardId)

	// TODO: 这里应该实际发放奖励到玩家背包

	logger.Info("Achievement claimed",
		zap.Uint64("player_id", playerInfo.GetPlayerId()),
		zap.Uint64("achievement_id", achievementId),
		zap.String("name", config.Name))

	return &pb.S2CAchievementClaim{
		Code:          pb.ResponseCode_normal.Enum(),
		AchievementId: &achievementId,
		Rewards:       rewards,
	}, nil
}
