package achievement

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"kairo_paradise_server/internal/I"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/game_config"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/models"
	"kairo_paradise_server/internal/time_manager"
	"kairo_paradise_server/services/pb"
	"strings"
)

// Manager 成就管理器
type Manager struct {
	achievements map[uint64]*models.Achievement // 成就配置缓存
	rareDrops    map[uint64]*models.RareDrop    // 稀缺掉落配置缓存
}

var GlobalManager *Manager

func init() {
	GlobalManager = NewManager()
}

// NewManager 创建成就管理器
func NewManager() *Manager {
	return &Manager{
		achievements: make(map[uint64]*models.Achievement),
		rareDrops:    make(map[uint64]*models.RareDrop),
	}
}

// LoadConfigs 加载成就配置
func (m *Manager) LoadConfigs() error {
	// 加载成就配置
	var achievements []models.Achievement
	err := bootstrap.MysqlDb.Where("is_active = ?", true).Find(&achievements).Error
	if err != nil {
		logger.Error("Failed to load achievements", zap.Error(err))
		return err
	}

	for _, achievement := range achievements {
		m.achievements[achievement.Id] = &achievement
	}

	// 加载稀缺掉落配置
	var rareDrops []models.RareDrop
	err = bootstrap.MysqlDb.Where("is_active = ?", true).Find(&rareDrops).Error
	if err != nil {
		logger.Error("Failed to load rare drops", zap.Error(err))
		return err
	}

	for _, rareDrop := range rareDrops {
		m.rareDrops[rareDrop.Id] = &rareDrop
	}

	logger.Info("Achievement configs loaded",
		zap.Int("achievements", len(m.achievements)),
		zap.Int("rare_drops", len(m.rareDrops)))

	return nil
}

// LoadPlayerAchievements 加载玩家成就数据
func LoadPlayerAchievements(p I.IPlayer) {
	playerId := p.GetPlayerId()

	// 加载玩家成就进度
	var playerAchievements []models.PlayerAchievement
	err := bootstrap.MysqlDb.Where("player_id = ?", playerId).Find(&playerAchievements).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error("Failed to load player achievements", zap.Error(err), zap.Uint64("player_id", playerId))
		return
	}

	// 加载玩家稀缺掉落记录
	var playerRareDrops []models.PlayerRareDrop
	err = bootstrap.MysqlDb.Where("player_id = ?", playerId).Find(&playerRareDrops).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error("Failed to load player rare drops", zap.Error(err), zap.Uint64("player_id", playerId))
		return
	}

	// 构建内存数据结构
	achievements := &models.PlayerAchievements{
		PlayerId:     playerId,
		Achievements: make(map[uint64]*models.PlayerAchievement),
		RareDrops:    make(map[uint64]*models.PlayerRareDrop),
		Statistics:   make(map[string]int64),
	}

	for _, pa := range playerAchievements {
		achievements.Achievements[pa.AchievementId] = &pa
	}

	for _, prd := range playerRareDrops {
		achievements.RareDrops[prd.RareDropId] = &prd
	}

	p.SetAchievements(achievements)
	logger.Debug("Player achievements loaded",
		zap.Uint64("player_id", playerId),
		zap.Int("achievements", len(achievements.Achievements)),
		zap.Int("rare_drops", len(achievements.RareDrops)))
}

// SavePlayerAchievements 保存玩家成就数据
func SavePlayerAchievements(p I.IPlayer) {
	achievements := p.GetAchievements()
	if achievements == nil {
		return
	}

	playerId := p.GetPlayerId()

	// 保存成就进度
	for _, pa := range achievements.Achievements {
		pa.UpdatedAt = time_manager.GlobalTimeManager.Now().Unix()
		err := bootstrap.MysqlDb.Save(pa).Error
		if err != nil {
			logger.Error("Failed to save player achievement",
				zap.Error(err),
				zap.Uint64("player_id", playerId),
				zap.Uint64("achievement_id", pa.AchievementId))
		}
	}

	// 保存稀缺掉落记录
	for _, prd := range achievements.RareDrops {
		err := bootstrap.MysqlDb.Save(prd).Error
		if err != nil {
			logger.Error("Failed to save player rare drop",
				zap.Error(err),
				zap.Uint64("player_id", playerId),
				zap.Uint64("rare_drop_id", prd.RareDropId))
		}
	}
}

// ProcessEvent 处理成就事件
func (m *Manager) ProcessEvent(p I.IPlayer, event *models.AchievementEvent) {
	achievements := p.GetAchievements()
	if achievements == nil {
		return
	}

	// 更新统计数据
	m.updateStatistics(achievements, event)

	// 检查所有相关成就
	for achievementId, config := range m.achievements {
		if config.GameId != 0 && config.GameId != event.GameId {
			continue // 跳过不相关的游戏成就
		}

		playerAchievement := achievements.Achievements[achievementId]
		if playerAchievement == nil {
			// 创建新的成就进度记录
			playerAchievement = &models.PlayerAchievement{
				PlayerId:      event.PlayerId,
				AchievementId: achievementId,
				Status:        models.AchievementStatusIncomplete,
				Progress:      0,
				MaxProgress:   1,
				CreatedAt:     time_manager.GlobalTimeManager.Now().Unix(),
				UpdatedAt:     time_manager.GlobalTimeManager.Now().Unix(),
			}
			achievements.Achievements[achievementId] = playerAchievement
		}

		// 跳过已完成的成就
		if playerAchievement.Status >= models.AchievementStatusClaimable {
			continue
		}

		// 检查成就条件
		if m.checkAchievementCondition(config, achievements, event) {
			m.completeAchievement(p, playerAchievement, config)
		}
	}
}

// updateStatistics 更新统计数据
func (m *Manager) updateStatistics(achievements *models.PlayerAchievements, event *models.AchievementEvent) {
	switch event.EventType {
	case "game_complete":
		key := fmt.Sprintf("game_%d_complete", event.GameId)
		achievements.Statistics[key]++
		achievements.Statistics["total_game_complete"]++

	case "game_time":
		if duration, ok := event.Data["duration"].(int64); ok {
			key := fmt.Sprintf("game_%d_time", event.GameId)
			achievements.Statistics[key] += duration
			achievements.Statistics["total_game_time"] += duration
		}

	case "enemy_kill":
		if count, ok := event.Data["count"].(int64); ok {
			key := fmt.Sprintf("game_%d_enemy_kill", event.GameId)
			achievements.Statistics[key] += count
			achievements.Statistics["total_enemy_kill"] += count
		}

	case "item_collect":
		if itemId, ok := event.Data["item_id"].(int32); ok {
			if count, ok := event.Data["count"].(int64); ok {
				key := fmt.Sprintf("item_%d_collect", itemId)
				achievements.Statistics[key] += count
				achievements.Statistics["total_item_collect"] += count
			}
		}

	case "level_reach":
		if level, ok := event.Data["level"].(int64); ok {
			achievements.Statistics["max_level"] = maxInt64(achievements.Statistics["max_level"], level)
		}

	case "online_time":
		if duration, ok := event.Data["duration"].(int64); ok {
			achievements.Statistics["total_online_time"] += duration
		}
	}
}

// checkAchievementCondition 检查成就条件
func (m *Manager) checkAchievementCondition(config *models.Achievement, achievements *models.PlayerAchievements, event *models.AchievementEvent) bool {
	// 获取条件配置
	conditionConfig := game_config.ConditionConfig.Item(config.ConditionId)
	if conditionConfig == nil {
		return false
	}

	// 根据条件类型进行判断
	switch conditionConfig.Type {
	case pb.EConditionType_EConditionType_GameComplete:
		// 完成特定游戏次数
		key := fmt.Sprintf("game_%d_complete", conditionConfig.Target)
		return achievements.Statistics[key] >= int64(*conditionConfig.Param1)

	case pb.EConditionType_EConditionType_GameTime:
		// 游戏时长达到要求
		key := fmt.Sprintf("game_%d_time", conditionConfig.Target)
		return achievements.Statistics[key] >= int64(*conditionConfig.Param1)

	case pb.EConditionType_EConditionType_EnemyKill:
		// 击杀敌人数量
		key := fmt.Sprintf("game_%d_enemy_kill", conditionConfig.Target)
		return achievements.Statistics[key] >= int64(*conditionConfig.Param1)

	case pb.EConditionType_EConditionType_ItemCollect:
		// 收集物品数量
		key := fmt.Sprintf("item_%d_collect", conditionConfig.Target)
		return achievements.Statistics[key] >= int64(*conditionConfig.Param1)

	case pb.EConditionType_EConditionType_LevelReach:
		// 达到等级
		return achievements.Statistics["max_level"] >= int64(*conditionConfig.Param1)

	case pb.EConditionType_EConditionType_OnlineTime:
		// 在线时长
		return achievements.Statistics["total_online_time"] >= int64(*conditionConfig.Param1)
	}

	return false
}

// completeAchievement 完成成就
func (m *Manager) completeAchievement(p I.IPlayer, playerAchievement *models.PlayerAchievement, config *models.Achievement) {
	now := time_manager.GlobalTimeManager.Now().Unix()
	playerAchievement.Status = models.AchievementStatusClaimable
	playerAchievement.Progress = playerAchievement.MaxProgress
	playerAchievement.CompletedAt = now
	playerAchievement.UpdatedAt = now

	// 发送成就完成通知
	awardConfig := game_config.AwardConfig.Item(config.AwardId)
	var rewards []*pb.ItemData
	if awardConfig != nil {
		for _, award := range awardConfig.Award {
			if award.ItemId != nil && award.Count != nil {
				rewards = append(rewards, &pb.ItemData{
					Id:    award.ItemId,
					Count: award.Count,
				})
			}
		}
	}

	p.WriteMsg(&pb.S2CAchievementComplete{
		AchievementId: &playerAchievement.AchievementId,
		Name:          &config.Name,
		Rarity:        &config.Rarity,
		Rewards:       rewards,
	})

	logger.Info("Achievement completed",
		zap.Uint64("player_id", p.GetPlayerId()),
		zap.Uint64("achievement_id", config.Id),
		zap.String("name", config.Name))
}

// TriggerEvent 触发成就事件
func TriggerEvent(p I.IPlayer, eventType string, gameId int32, data map[string]interface{}) {
	if GlobalManager == nil {
		return
	}

	event := &models.AchievementEvent{
		PlayerId:  p.GetPlayerId(),
		EventType: eventType,
		GameId:    gameId,
		Data:      data,
		Timestamp: time_manager.GlobalTimeManager.Now().Unix(),
	}

	GlobalManager.ProcessEvent(p, event)
}

// maxInt64 返回两个int64中的较大值
func maxInt64(a, b int64) int64 {
	if a > b {
		return a
	}
	return b
}
