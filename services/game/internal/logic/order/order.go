package order

import (
	"errors"
	"fmt"
	"kairo_paradise_server/internal/I"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/common"
	"kairo_paradise_server/internal/game_config"
	"kairo_paradise_server/internal/models"
	"kairo_paradise_server/internal/time_manager"
	"kairo_paradise_server/services/pb"
)

func CreateGameOrder(player I.IPlayer, gameInfo *pb.ListMiniGameConfig_MiniGameConfig) (orderId string, err error) {
	rechargeInfo := game_config.RechargeConfig.Item(*gameInfo.RechargeId)
	if rechargeInfo == nil {
		return "", errors.New("recharge not found")
	}
	if rechargeInfo.Type == nil || *rechargeInfo.Type != pb.ERechargeType_ERechargeType_MiniGame {
		return "", errors.New("recharge type error")
	}

	order := models.Order{
		OrderId:    genOrderId(player.GetUserId()),
		Uid:        player.GetUserId(),
		PlayerId:   player.GetPlayerId(),
		RechargeId: *gameInfo.RechargeId,
		Identity:   *rechargeInfo.Identity,
		Amount:     *rechargeInfo.Price,
		Currency:   1,
		CreatedAt:  time_manager.GlobalTimeManager.Now().Unix(),
	}
	order.PlatformId, order.ChannelNo = player.GetPlatformInfo()
	if err := bootstrap.MysqlDb.Create(&order).Error; err != nil {
		return "", err
	}
	return order.OrderId, nil
}

func genOrderId(userId uint64) string {
	timestamp := time_manager.GlobalTimeManager.Now().Format("060102150405")
	uidPart := fmt.Sprintf("%02d", userId%100)
	randomPart := fmt.Sprintf("%04d", common.Rnd.Intn(10000))
	orderId := timestamp + uidPart + randomPart
	return orderId
}
