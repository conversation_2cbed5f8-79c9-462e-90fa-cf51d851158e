package servo

import (
	"context"
	"encoding/json"
	"kairo_paradise_server/internal/config"
	"kairo_paradise_server/services/servo/internal/config_manager"
	"os"
	"path/filepath"
)

func Initialize(ctx context.Context) {
	loadServerJson()
}

func loadServerJson() {
	configPath := filepath.Join(config.ConfigPath, "server.json")
	if _, err := os.Stat(configPath); err != nil {
		return
	}

	file, err := os.Open(configPath)
	if err != nil {
		return
	}
	defer file.Close()

	var jsonData map[string]interface{}
	if err := json.NewDecoder(file).Decode(&jsonData); err != nil {
		return
	}

	config_manager.Manager.Set(jsonData)
}
