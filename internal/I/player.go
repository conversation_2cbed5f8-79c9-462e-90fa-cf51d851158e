package I

import "kairo_paradise_server/internal/models"

type IPlayer interface {
	GetPlayerId() uint64
	GetUserId() uint64

	GetUserInfo() *models.UserInfo
	GetPlayerInfo() *models.Player

	GetGames() *models.PlayerGames
	SetGames(*models.PlayerGames)

	GetAssets() *models.Assets
	SetAssets(assets *models.Assets)

	GetScene() *models.Scene
	SetScene(scene *models.Scene)

	GetMailBox() *models.PlayerMailBox
	SetMailBox(mailBox *models.PlayerMailBox)

	GetAnnouncementBox() *models.PlayerAnnouncementBox
	SetAnnouncementBox(announcementBox *models.PlayerAnnouncementBox)

	GetMonthlyCard() *models.MonthlyCard
	SetMonthlyCard(monthlyCard *models.MonthlyCard)

	GetAchievements() *models.PlayerAchievements
	SetAchievements(achievements *models.PlayerAchievements)

	IsRobot() bool

	IsRegister() bool

	SetPlatformInfo(platformId int32, channelNo string)
	GetPlatformInfo() (platformId int32, channelNo string)

	SetServices(serviceStr string)
	SetUserInfo(userInfo *models.UserInfo)
	SetInfo(info *models.Player)
	SetIsReconnect(isReconnect bool)

	OnAfterLoad()
	OnLoginSuccess()
	OnDisconnect()
	OnDestroy()
	CleanupCancel()
	SetCleanupCancel(cancel func())

	WriteMsg(message interface{})
	WritePump()

	Ticker()
}
