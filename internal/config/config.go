package config

import (
	"fmt"
	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
	"os"
	"path/filepath"
	"time"
)

var (
	ServerName  string
	Path        string
	Environment string
	ConfigPath  string
)

type MysqlConf struct {
	Host               string `mapstructure:"host"`
	Port               string `mapstructure:"port"`
	User               string `mapstructure:"user"`
	Password           string `mapstructure:"password"`
	Database           string `mapstructure:"database"`
	Loc                string `mapstructure:"loc"`
	Prefix             string `mapstructure:"prefix"`
	Charset            string `mapstructure:"charset"`
	SetMaxIdleConn     int    `mapstructure:"set_max_idle_conn"`
	SetMaxOpenConn     int    `mapstructure:"set_max_open_conn"`
	SetConnMaxLifeTime int    `mapstructure:"set_conn_max_life_time"`
	SetConnMaxIdleTime int    `mapstructure:"set_conn_max_idle_time"`
	LogLevel           string `mapstructure:"log_level"`
}

type RedisConf struct {
	Host   string `mapstructure:"host"`
	Port   string `mapstructure:"port"`
	Passwd string `mapstructure:"passwd"`
	Index  int    `mapstructure:"index"`
}

// ServerConfig holds server-related configuration
type ServerConfig struct {
	Addr     string `mapstructure:"addr"`
	GRpcAddr string `mapstructure:"grpc_addr"`
}

// JWTConfig holds JWT-related configuration
type JWTConfig struct {
	SecretKey          string        `mapstructure:"secret_key"`
	AccessTokenExpiry  time.Duration `mapstructure:"access_token_expiry"`
	RefreshTokenExpiry time.Duration `mapstructure:"refresh_token_expiry"`
	Issuer             string        `mapstructure:"issuer"`
}

// LoggerConfig holds logger-related configuration
type LoggerConfig struct {
	LogLevel         string   `mapstructure:"log_level"`
	OutputPaths      []string `mapstructure:"output_paths"`
	ErrorOutputPaths []string `mapstructure:"error_output_paths"`
	Debug            bool     `mapstructure:"debug"`
	Encoding         string   `mapstructure:"encoding"`
	MaxSize          int      `mapstructure:"max_size"`
	MaxBackups       int      `mapstructure:"max_backups"`
	MaxAge           int      `mapstructure:"max_age"`
	Compress         bool     `mapstructure:"compress"`
}

// EtcdConfig holds etcd-related configuration
type EtcdConfig struct {
	Endpoints        []string      `mapstructure:"endpoints"`
	DialTimeout      time.Duration `mapstructure:"dial_timeout"`
	Username         string        `mapstructure:"username"`
	Password         string        `mapstructure:"password"`
	ServiceTTL       time.Duration `mapstructure:"service_ttl"`
	ServicePrefix    string        `mapstructure:"service_prefix"`
	AutoSync         bool          `mapstructure:"auto_sync"`
	AutoSyncInterval time.Duration `mapstructure:"auto_sync_interval"`
}

type DetectionConfig struct {
	Domain string `mapstructure:"domain"`
	Game   string `mapstructure:"game"`
	Key    string `mapstructure:"key"`
	AppId  string `mapstructure:"app_id"`
}

var Config = struct {
	MysqlConf     MysqlConf       `mapstructure:"mysql"`
	RedisConf     RedisConf       `mapstructure:"redis"`
	JWTConf       JWTConfig       `mapstructure:"jwt"`
	LoggerConf    LoggerConfig    `mapstructure:"logger"`
	EtcdConf      EtcdConfig      `mapstructure:"etcd"`
	DetectionConf DetectionConfig `mapstructure:"detection"`
	Debug         bool            `mapstructure:"debug"`
}{}

var ServerConf = struct {
	ServerConfig ServerConfig `mapstructure:"server"`
	Domain       string       `mapstructure:"domain"`
}{}

func InitViper(name, path string) *viper.Viper {
	v := viper.New()
	v.SetConfigName(name)
	v.SetConfigType("yaml")
	v.AddConfigPath(path)

	if err := v.ReadInConfig(); err != nil {
		panic(fmt.Errorf("fatal error config file %s: %w", name, err))
	}
	// 监听变化
	v.WatchConfig()
	v.OnConfigChange(func(e fsnotify.Event) {
		// 配置文件变化后重新读取
		fmt.Println("Config file changed:", e.Name)
	})
	return v
}

func GetEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

func InitServerConfig(serverName string) {
	ServerName = serverName
	serverViper := InitViper(ServerName, Path)
	if err := serverViper.Unmarshal(&ServerConf); err != nil {
		panic(err)
	}
}

func IsDevelop() bool {
	if Environment == "develop" {
		return true
	}
	return false
}

func IsTesting() bool {
	if Environment == "testing" {
		return true
	}
	return false
}

func IsProd() bool {
	if Environment == "prod" {
		return true
	}
	return false
}

func init() {
	Environment = GetEnv("ENVIRONMENT", "prod")
	pathList := []string{"./config", "../bin/config", "../../../bin/config"}
	for _, path := range pathList {
		if _, err := os.Stat(path); err == nil {
			Path = path
			break
		}
	}

	paths := GetEnv("ENVIRONMENT", "")
	if paths != "" {
		Path = filepath.Join(Path, paths)
	}
	ConfigPath = Path

	configViper := InitViper("config", Path)

	if err := configViper.Unmarshal(&Config); err != nil {
		panic(err)
	}
}
