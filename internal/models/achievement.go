package models

// Achievement 成就数据库模型
type Achievement struct {
	Id          uint64 `gorm:"primaryKey;autoIncrement"`
	Name        string `gorm:"column:name;type:varchar(255);not null"`                                    // 成就名称
	Description string `gorm:"column:description;type:text"`                                              // 成就描述
	GameId      int32  `gorm:"column:game_id;type:int;not null;index:idx_achievement_game"`              // 所属游戏ID
	Icon        string `gorm:"column:icon;type:varchar(255)"`                                             // 成就图标
	Rarity      int32  `gorm:"column:rarity;type:int;not null;default:1"`                                // 稀有度 1=普通 2=稀有 3=史诗 4=传说
	AwardId     int32  `gorm:"column:award_id;type:int;not null"`                                         // 奖励配置ID
	ConditionId int32  `gorm:"column:condition_id;type:int;not null"`                                     // 达成条件配置ID
	SortOrder   int32  `gorm:"column:sort_order;type:int;not null;default:0"`                            // 排序权重
	IsActive    bool   `gorm:"column:is_active;type:boolean;not null;default:true"`                      // 是否激活
	CreatedAt   int64  `gorm:"column:created_at;type:bigint;not null"`                                    // 创建时间
	UpdatedAt   int64  `gorm:"column:updated_at;type:bigint;not null"`                                    // 更新时间
}

func (a *Achievement) TableName() string {
	return "t_achievement"
}

// PlayerAchievement 玩家成就进度数据库模型
type PlayerAchievement struct {
	Id            uint64 `gorm:"primaryKey;autoIncrement"`
	PlayerId      uint64 `gorm:"column:player_id;type:bigint;not null;index:idx_player_achievement"`     // 玩家ID
	AchievementId uint64 `gorm:"column:achievement_id;type:bigint;not null;index:idx_player_achievement"` // 成就ID
	Status        int32  `gorm:"column:status;type:int;not null;default:0"`                              // 状态 0=未完成 1=可领取 2=已领取
	Progress      int64  `gorm:"column:progress;type:bigint;not null;default:0"`                         // 当前进度
	MaxProgress   int64  `gorm:"column:max_progress;type:bigint;not null;default:1"`                     // 最大进度
	CompletedAt   int64  `gorm:"column:completed_at;type:bigint;default:0"`                              // 完成时间
	ClaimedAt     int64  `gorm:"column:claimed_at;type:bigint;default:0"`                                // 领取时间
	CreatedAt     int64  `gorm:"column:created_at;type:bigint;not null"`                                 // 创建时间
	UpdatedAt     int64  `gorm:"column:updated_at;type:bigint;not null"`                                 // 更新时间
}

func (pa *PlayerAchievement) TableName() string {
	return "t_player_achievement"
}

// RareDrop 稀缺掉落数据库模型
type RareDrop struct {
	Id          uint64 `gorm:"primaryKey;autoIncrement"`
	Name        string `gorm:"column:name;type:varchar(255);not null"`                    // 掉落名称
	Description string `gorm:"column:description;type:text"`                              // 掉落描述
	Icon        string `gorm:"column:icon;type:varchar(255)"`                             // 掉落图标
	Rarity      int32  `gorm:"column:rarity;type:int;not null;default:1"`                // 稀有度
	AwardId     int32  `gorm:"column:award_id;type:int;not null"`                         // 奖励配置ID
	DropRate    int32  `gorm:"column:drop_rate;type:int;not null;default:1"`             // 掉落概率(万分比)
	IsActive    bool   `gorm:"column:is_active;type:boolean;not null;default:true"`      // 是否激活
	CreatedAt   int64  `gorm:"column:created_at;type:bigint;not null"`                   // 创建时间
	UpdatedAt   int64  `gorm:"column:updated_at;type:bigint;not null"`                   // 更新时间
}

func (rd *RareDrop) TableName() string {
	return "t_rare_drop"
}

// PlayerRareDrop 玩家稀缺掉落记录
type PlayerRareDrop struct {
	Id         uint64 `gorm:"primaryKey;autoIncrement"`
	PlayerId   uint64 `gorm:"column:player_id;type:bigint;not null;index:idx_player_rare_drop"` // 玩家ID
	RareDropId uint64 `gorm:"column:rare_drop_id;type:bigint;not null"`                         // 稀缺掉落ID
	ObtainedAt int64  `gorm:"column:obtained_at;type:bigint;not null"`                          // 获得时间
	Source     string `gorm:"column:source;type:varchar(255)"`                                  // 获得来源
	CreatedAt  int64  `gorm:"column:created_at;type:bigint;not null"`                           // 创建时间
}

func (prd *PlayerRareDrop) TableName() string {
	return "t_player_rare_drop"
}

// 成就状态枚举
const (
	AchievementStatusIncomplete = 0 // 未完成
	AchievementStatusClaimable  = 1 // 可领取
	AchievementStatusClaimed    = 2 // 已领取
)

// 稀有度枚举
const (
	RarityCommon    = 1 // 普通
	RarityRare      = 2 // 稀有
	RarityEpic      = 3 // 史诗
	RarityLegendary = 4 // 传说
)

// 内存中的成就数据结构
type PlayerAchievements struct {
	PlayerId     uint64                           `json:"player_id"`
	Achievements map[uint64]*PlayerAchievement    `json:"achievements"`     // 成就进度
	RareDrops    map[uint64]*PlayerRareDrop       `json:"rare_drops"`       // 稀缺掉落记录
	Statistics   map[string]int64                 `json:"statistics"`       // 统计数据，用于条件判断
}

// AchievementCondition 成就条件数据结构
type AchievementCondition struct {
	Type      string                 `json:"type"`       // 条件类型
	Target    string                 `json:"target"`     // 目标对象
	Operation string                 `json:"operation"`  // 操作类型 (>=, ==, >, <, etc.)
	Value     int64                  `json:"value"`      // 目标值
	Params    map[string]interface{} `json:"params"`     // 额外参数
}

// AchievementEvent 成就事件数据结构
type AchievementEvent struct {
	PlayerId  uint64                 `json:"player_id"`
	EventType string                 `json:"event_type"`
	GameId    int32                  `json:"game_id"`
	Data      map[string]interface{} `json:"data"`
	Timestamp int64                  `json:"timestamp"`
}
