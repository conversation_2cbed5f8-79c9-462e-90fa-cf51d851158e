package models

import (
	"encoding/json"
	"gorm.io/gorm"
)

type ItemData struct {
	Id    int32 `json:"id"`
	Count int32 `json:"count"`
}

// PlayerMail represents a mail in the database
type PlayerMail struct {
	Id           uint64     `gorm:"primaryKey;autoIncrement"`
	PlayerId     uint64     `gorm:"column:player_id;index"`
	MailId       int32      `gorm:"column:mail_id"`                            // Mail template ID from config
	Title        string     `gorm:"column:title;type:varchar(128);not null"`   // Mail title
	Content      string     `gorm:"column:content;type:text;not null"`         // Mail content
	Sender       string     `gorm:"column:sender;type:varchar(64);not null"`   // Mail sender
	Status       int32      `gorm:"column:status;type:int;not null;default:1"` // Mail status: 1=unread, 2=read, 3=claimed
	AttachmentsT []byte     `gorm:"column:attachments;type:blob"`              // Serialized attachments
	Attachments  []ItemData `gorm:"-"`
	CreatedAt    int64      `gorm:"column:created_at;type:bigint;not null"`           // Creation timestamp
	ExpireAt     int64      `gorm:"column:expire_at;type:bigint;not null;default:0"`  // Expiration timestamp (0 = never expires)
	DeletedAt    int64      `gorm:"column:deleted_at;type:bigint;not null;default:0"` // Soft delete timestamp
}

func (m *PlayerMail) TableName() string {
	return "t_player_mail"
}

func (m *PlayerMail) AfterFind(tx *gorm.DB) error {
	if len(m.AttachmentsT) > 0 {
		_ = json.Unmarshal(m.AttachmentsT, &m.Attachments)
	}
	return nil
}

func (m *PlayerMail) BeforeCreate(tx *gorm.DB) error {
	if len(m.Attachments) > 0 {
		attachmentsT, _ := json.Marshal(m.Attachments)
		m.AttachmentsT = attachmentsT
	}
	return nil
}

// Mail represents a mail in memory with deserialized attachments
type Mail struct {
	Id          uint64
	MailId      int32
	Title       string
	Content     string
	Sender      string
	Status      int32
	Attachments []ItemData
	CreatedAt   int64
	ExpireAt    int64
}

// PlayerMailBox represents a player's mailbox in memory
type PlayerMailBox struct {
	PlayerId uint64
	Mails    map[uint64]*Mail // Map of mail ID to mail
	maxId    uint64
}

func (m *PlayerMailBox) GetMaxId() uint64 {
	return m.maxId
}

func (m *PlayerMailBox) NextId() uint64 {
	m.maxId++
	return m.maxId
}

func (m *PlayerMailBox) SetMaxId(id uint64) {
	m.maxId = id
}

func (m *PlayerMailBox) CalculateMaxId() uint64 {
	maxID := uint64(0)
	for id := range m.Mails {
		if id > maxID {
			maxID = id
		}
	}
	return maxID
}

// NewPlayerMailBox creates a new player mailbox
func NewPlayerMailBox(playerId uint64) *PlayerMailBox {
	return &PlayerMailBox{
		PlayerId: playerId,
		Mails:    make(map[uint64]*Mail),
	}
}
