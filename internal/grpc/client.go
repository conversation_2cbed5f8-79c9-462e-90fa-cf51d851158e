package grpc

import (
	"context"
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"kairo_paradise_server/services/pb/msg"
	"strconv"
	"sync"
)

// Client represents a gRPC client connection to a game server
type Client struct {
	conn      *grpc.ClientConn
	addr      string
	mu        sync.Mutex
	closed    bool
	rpcClient *pb.GRpcServiceClient
	uid       uint64 // 存储用户UID，用于自动添加到请求上下文中
}

// NewClient creates a new gRPC client
func NewClient(addr string) (*Client, error) {
	conn, err := grpc.NewClient(addr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, err
	}

	// 创建RpcServiceClient
	c := pb.NewGRpcServiceClient(conn)

	client := &Client{
		conn:      conn,
		addr:      addr,
		rpcClient: &c,
	}

	return client, nil
}

// NewClientWithUID creates a new gRPC client with UID
func NewClientWithUID(addr string, uid uint64) (*Client, error) {
	client, err := NewClient(addr)
	if err != nil {
		return nil, err
	}

	// 设置UID
	client.uid = uid

	return client, nil
}

func (c *Client) GetConn() *grpc.ClientConn {
	return c.conn
}

// Rpc returns the gRPC client
// 注意：这个方法返回的是原始客户端，不会自动添加上下文数据
func (c *Client) Rpc() pb.GRpcServiceClient {
	return *c.rpcClient
}

// SetUID 设置客户端的UID，用于自动添加到请求上下文中
func (c *Client) SetUID(uid uint64) {
	c.uid = uid
}

// GetUID 获取客户端的UID
func (c *Client) GetUID() uint64 {
	return c.uid
}

// RpcWithContext 返回一个包装了上下文数据的gRPC客户端
// 这个方法会自动将UID添加到请求上下文中
type contextAwareGRpcClient struct {
	client *Client
}

func (c *Client) InitRpc(services map[string]string, channelNo string, platformId int32) error {
	b, _ := json.Marshal(services)
	serv := string(b)
	protoId := uint32(msg.PCK_G2SInit)
	p := pb.G2SInit{
		Services:   &serv,
		ChannelNo:  &channelNo,
		PlatformId: &platformId,
	}
	b, _ = proto.Marshal(&p)

	req := &pb.G2SRequest{
		ProtocolId: &protoId,
		Payload:    b,
	}
	_, err := c.G2SRpc(context.Background(), req)
	if err != nil {
		return err
	}
	return nil
}

// G2SRpc 重写G2SRpc方法，自动添加UID到上下文中
func (c *Client) G2SRpc(ctx context.Context, req *pb.G2SRequest, opts ...grpc.CallOption) (*pb.S2GRequest, error) {
	// 如果UID已设置，则自动添加到上下文中
	if c.uid > 0 {
		// 创建带有UID的元数据
		md := metadata.New(map[string]string{
			"uid": strconv.FormatUint(c.uid, 10),
		})
		// 将元数据添加到上下文中
		ctx = metadata.NewOutgoingContext(ctx, md)
	}

	// 调用原始方法
	return c.Rpc().G2SRpc(ctx, req, opts...)
}

// S2GRpc 实现S2GRpc方法以满足接口要求
func (c *Client) S2GRpc(ctx context.Context, req *pb.S2GRequest, opts ...grpc.CallOption) (*pb.Empty, error) {
	// 如果UID已设置，则自动添加到上下文中
	if c.uid > 0 {
		// 创建带有UID的元数据
		md := metadata.New(map[string]string{
			"uid": strconv.FormatUint(c.uid, 10),
		})

		// 将元数据添加到上下文中
		ctx = metadata.NewOutgoingContext(ctx, md)
	}

	// 调用原始方法
	return c.Rpc().S2GRpc(ctx, req, opts...)
}

// Close closes the gRPC connection
func (c *Client) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.closed {
		return nil
	}

	c.closed = true
	logger.Infof("Closing gRPC connection to %s", c.addr)
	return c.conn.Close()
}
