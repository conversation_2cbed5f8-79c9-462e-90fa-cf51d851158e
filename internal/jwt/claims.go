package jwt

import (
	"github.com/golang-jwt/jwt/v5"
	"kairo_paradise_server/internal/time_manager"
	"strconv"
	"time"
)

// CustomClaims extends the standard jwt.RegisteredClaims
// to include custom fields for our application
type CustomClaims struct {
	jwt.RegisteredClaims
	Uid        uint64            `json:"uid"`
	UserType   uint              `json:"user_type"`
	ChannelNo  string            `json:"channel_no"`
	PlatformId int32             `json:"platform_id"`
	Services   map[string]string `json:"services"`
}

type LoginClaims struct {
	Uid        uint64            `json:"uid"`
	UserType   uint              `json:"user_type"`
	ChannelNo  string            `json:"channel_no"`
	PlatformId int32             `json:"platform_id"`
	Services   map[string]string `json:"services"`
}

// NewAccessTokenClaims creates claims for an access token
func NewAccessTokenClaims(loginClaims LoginClaims, expiry time.Duration) CustomClaims {
	now := time_manager.GlobalTimeManager.Now()
	return CustomClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(expiry)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "kairo_paradise_server",
			Subject:   strconv.Itoa(int(loginClaims.Uid)),
		},
		Uid:        loginClaims.Uid,
		UserType:   loginClaims.UserType,
		Services:   loginClaims.Services,
		ChannelNo:  loginClaims.ChannelNo,
		PlatformId: loginClaims.PlatformId,
	}
}

// NewRefreshTokenClaims creates claims for a refresh token
func NewRefreshTokenClaims(loginClaims LoginClaims, expiry time.Duration) CustomClaims {
	now := time_manager.GlobalTimeManager.Now()
	return CustomClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(expiry)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "kairo_paradise_server",
			Subject:   strconv.Itoa(int(loginClaims.Uid)),
		},
		Uid:        loginClaims.Uid,
		UserType:   loginClaims.UserType,
		Services:   loginClaims.Services,
		ChannelNo:  loginClaims.ChannelNo,
		PlatformId: loginClaims.PlatformId,
	}
}
