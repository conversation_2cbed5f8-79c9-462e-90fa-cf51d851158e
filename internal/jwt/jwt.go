package jwt

import (
	"errors"
	"github.com/golang-jwt/jwt/v5"
	"time"
)

// Common errors
var (
	ErrInvalidToken      = errors.New("invalid token")
	ErrExpiredToken      = errors.New("token has expired")
	ErrTokenNotValidYet  = errors.New("token not valid yet")
	ErrInvalidSigningKey = errors.New("invalid signing key")
	ErrInvalidClaims     = errors.New("invalid claims")
)

// TokenType represents the type of token
type TokenType string

const (
	// AccessToken is used for API access
	AccessToken TokenType = "access"
	// RefreshToken is used to get a new access token
	RefreshToken TokenType = "refresh"
)

// TokenPair represents a pair of access and refresh tokens
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

// Service provides JWT token operations
type Service struct {
	config Config
}

// NewService creates a new JWT service with the given configuration
func NewService(config Config) *Service {
	return &Service{
		config: config,
	}
}

// NewServiceWithDefaults creates a new JWT service with default configuration
func NewServiceWithDefaults() *Service {
	return NewService(DefaultConfig())
}

// GenerateToken generates a new JWT token with the given claims
func (s *Service) GenerateToken(claims CustomClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.config.SecretKey))
}

// GenerateAccessToken generates a new access token for the given user
func (s *Service) GenerateAccessToken(loginClaims LoginClaims) (string, error) {
	claims := NewAccessTokenClaims(loginClaims, s.config.AccessTokenExpiry)
	return s.GenerateToken(claims)
}

// GenerateRefreshToken generates a new refresh token for the given user
func (s *Service) GenerateRefreshToken(loginClaims LoginClaims) (string, error) {
	claims := NewRefreshTokenClaims(loginClaims, s.config.RefreshTokenExpiry)
	return s.GenerateToken(claims)
}

// GenerateTokenPair generates both access and refresh tokens
func (s *Service) GenerateTokenPair(loginClaims LoginClaims) (TokenPair, error) {
	accessToken, err := s.GenerateAccessToken(loginClaims)
	if err != nil {
		return TokenPair{}, err
	}

	refreshToken, err := s.GenerateRefreshToken(loginClaims)
	if err != nil {
		return TokenPair{}, err
	}

	return TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
	}, nil
}

// ParseToken parses and validates a JWT token
func (s *Service) ParseToken(tokenString string) (*CustomClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate the signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, ErrInvalidSigningKey
		}
		return []byte(s.config.SecretKey), nil
	})

	if err != nil {
		// Check for specific error types
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, ErrExpiredToken
		} else if errors.Is(err, jwt.ErrTokenNotValidYet) {
			return nil, ErrTokenNotValidYet
		}
		return nil, ErrInvalidToken
	}

	// Extract claims
	if claims, ok := token.Claims.(*CustomClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, ErrInvalidClaims
}

// ValidateToken validates a JWT token and returns the claims if valid
func (s *Service) ValidateToken(tokenString string) (*CustomClaims, error) {
	return s.ParseToken(tokenString)
}

// RefreshTokens validates a refresh token and generates a new token pair
func (s *Service) RefreshTokens(refreshToken string) (TokenPair, error) {
	// Parse and validate the refresh token
	claims, err := s.ParseToken(refreshToken)
	if err != nil {
		return TokenPair{}, err
	}

	// Generate a new token pair
	return s.GenerateTokenPair(LoginClaims{
		Uid:        claims.Uid,
		UserType:   claims.UserType,
		ChannelNo:  claims.ChannelNo,
		PlatformId: claims.PlatformId,
		Services:   claims.Services,
	})
}

// RefreshToken generates a new access token from a refresh token
func (s *Service) RefreshToken(refreshToken string) (string, error) {
	claims, err := s.ParseToken(refreshToken)
	if err != nil {
		return "", err
	}
	return s.GenerateAccessToken(LoginClaims{
		Uid:        claims.Uid,
		UserType:   claims.UserType,
		ChannelNo:  claims.ChannelNo,
		PlatformId: claims.PlatformId,
		Services:   claims.Services,
	})
}

// GetUidFromToken extracts the user ID from a token
func (s *Service) GetUidFromToken(tokenString string) (uint64, error) {
	claims, err := s.ParseToken(tokenString)
	if err != nil {
		return 0, err
	}
	return claims.Uid, nil
}

// GetUserUserTypeFromToken extracts the user account from a token
func (s *Service) GetUserUserTypeFromToken(tokenString string) (uint, error) {
	claims, err := s.ParseToken(tokenString)
	if err != nil {
		return 1, err
	}
	return claims.UserType, nil
}

// IsTokenExpired checks if a token is expired
func (s *Service) IsTokenExpired(tokenString string) bool {
	_, err := s.ParseToken(tokenString)
	return errors.Is(err, ErrExpiredToken)
}

// GetTokenExpiration returns the expiration time of a token
func (s *Service) GetTokenExpiration(tokenString string) (time.Time, error) {
	claims, err := s.ParseToken(tokenString)
	if err != nil {
		return time.Time{}, err
	}

	expTime, err := claims.GetExpirationTime()
	if err != nil {
		return time.Time{}, err
	}

	return expTime.Time, nil
}
