package main

import (
	"context"
	"github.com/gin-gonic/gin"
	"kairo_paradise_server/internal/bootstrap"
	"kairo_paradise_server/internal/config"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/internal/middleware"
	"kairo_paradise_server/internal/serv"
	"kairo_paradise_server/services/servo"
	"kairo_paradise_server/services/servo/external"
	"net/http"
)

func main() {
	bootstrap.Bootstrap("servo")

	// Start HTTP game
	startHttpServ()
}

func startHttpServ() {
	r := gin.New()

	if config.Config.Debug {
		gin.SetMode(gin.DebugMode)
	}

	r.Use(middleware.CorsMiddleware(), middleware.RecoverMiddleware(true), middleware.LoggerMiddleware())

	servo.Initialize(context.Background())

	external.RegisterRouter(r)

	srv := &http.Server{
		Addr:    config.ServerConf.ServerConfig.Addr,
		Handler: r,
	}
	logger.Infof("Listen addr [%s]", config.ServerConf.ServerConfig.Addr)

	serv.ListenAndServe(srv)
}
