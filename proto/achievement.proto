syntax = "proto2";

package pb;

option go_package = "kairo_paradise_server/services/pb;pb";

import "gameconfig.proto";
import "code.proto";

// 成就信息
message AchievementInfo {
    optional uint64 id = 1;                    // 成就ID
    optional string name = 2;                  // 成就名称
    optional string description = 3;           // 成就描述
    optional int32 game_id = 4;               // 所属游戏ID
    optional string game_name = 5;            // 游戏名称
    optional string icon = 6;                 // 成就图标
    optional int32 rarity = 7;               // 稀有度
    optional int32 status = 8;               // 状态 0=未完成 1=可领取 2=已领取
    optional int64 progress = 9;             // 当前进度
    optional int64 max_progress = 10;        // 最大进度
    optional int64 completed_at = 11;        // 完成时间
    optional int64 claimed_at = 12;          // 领取时间
    optional float completion_rate = 13;     // 达成百分比 (完成人数/玩该游戏人数)
    repeated ItemData rewards = 14;          // 奖励列表
}

// 稀缺掉落信息
message RareDropInfo {
    optional uint64 id = 1;                   // 稀缺掉落ID
    optional string name = 2;                 // 掉落名称
    optional string description = 3;          // 掉落描述
    optional string icon = 4;                 // 掉落图标
    optional int32 rarity = 5;               // 稀有度
    optional int64 obtained_at = 6;          // 获得时间
    optional string source = 7;              // 获得来源
    repeated ItemData rewards = 8;           // 奖励列表
}

// 获取成就列表请求 10400
message C2SAchievementList {
    optional int32 tab_type = 1;             // 标签类型 0=全部 1=未完成 2=已领取 3=可领取 4=稀缺掉落
    optional int32 game_id = 2;              // 游戏ID筛选，0表示所有游戏
    optional int32 page = 3;                 // 页码，从1开始
    optional int32 page_size = 4;            // 每页数量，默认20
    optional string search_keyword = 5;      // 搜索关键词
}

// 获取成就列表响应 10401
message S2CAchievementList {
    optional response_code code = 1;
    optional string message = 2;
    repeated AchievementInfo achievements = 3;  // 成就列表
    repeated RareDropInfo rare_drops = 4;       // 稀缺掉落列表
    optional int32 total_count = 5;             // 总数量
    optional int32 current_page = 6;            // 当前页码
    optional int32 total_pages = 7;             // 总页数
}

// 领取成就奖励请求 10402
message C2SAchievementClaim {
    optional uint64 achievement_id = 1;       // 成就ID
}

// 领取成就奖励响应 10403
message S2CAchievementClaim {
    optional response_code code = 1;
    optional string message = 2;
    optional uint64 achievement_id = 3;       // 成就ID
    repeated ItemData rewards = 4;            // 获得的奖励
}

// 成就进度更新通知
message S2CAchievementProgress {
    optional uint64 achievement_id = 1;       // 成就ID
    optional int64 progress = 2;             // 当前进度
    optional int64 max_progress = 3;         // 最大进度
    optional int32 status = 4;               // 新状态
}

// 成就完成通知
message S2CAchievementComplete {
    optional uint64 achievement_id = 1;       // 成就ID
    optional string name = 2;                 // 成就名称
    optional int32 rarity = 3;               // 稀有度
    repeated ItemData rewards = 4;            // 奖励列表
}

// 稀缺掉落获得通知
message S2CRareDropObtained {
    optional uint64 rare_drop_id = 1;         // 稀缺掉落ID
    optional string name = 2;                 // 掉落名称
    optional int32 rarity = 3;               // 稀有度
    optional string source = 4;              // 获得来源
    repeated ItemData rewards = 5;            // 奖励列表
}

// 搜索游戏请求 10404
message C2SAchievementSearchGame {
    optional string keyword = 1;              // 搜索关键词
}

// 搜索游戏响应 10405
message S2CAchievementSearchGame {
    optional response_code code = 1;
    optional string message = 2;
    repeated GameSearchResult games = 3;      // 游戏搜索结果
}

// 游戏搜索结果
message GameSearchResult {
    optional int32 game_id = 1;              // 游戏ID
    optional string game_name = 2;           // 游戏名称
    optional string icon = 3;                // 游戏图标
    optional int32 achievement_count = 4;    // 成就数量
    optional int32 completed_count = 5;      // 已完成数量
}

// 成就统计信息
message AchievementStatistics {
    optional int32 total_achievements = 1;    // 总成就数
    optional int32 completed_achievements = 2; // 已完成成就数
    optional int32 claimable_achievements = 3; // 可领取成就数
    optional int32 rare_drops_count = 4;      // 稀缺掉落数量
    optional float completion_percentage = 5;  // 完成百分比
}

// 获取成就统计请求 10406
message C2SAchievementStatistics {
}

// 获取成就统计响应 10407
message S2CAchievementStatistics {
    optional response_code code = 1;
    optional string message = 2;
    optional AchievementStatistics statistics = 3;
}
